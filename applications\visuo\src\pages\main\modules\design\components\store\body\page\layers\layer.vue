<template>
  <div class="vis-store-page-container" :style="{ height: `calc(100vh - ${pagesHeight}px - 129px)` }">
    <!-- 头部 -->
    <div class="vis-store-page-container-header" v-show="!isSearchHide">
      <span class="vis-store-page-container-title">图层</span>
      <div class="vis-store-page-container-btn-group">
        <q-btn class="vis-store-page-container-btn" flat size="8px" @click="onUpdateSearchHide">
          <ht-icon class="vis-icon" name="hticon-vis-search"></ht-icon>
          <q-tooltip> 搜索 </q-tooltip>
        </q-btn>
        <q-btn class="vis-store-page-container-btn" flat size="8px" @click="onUpdateExpandAllHide">
          <ht-icon
            class="vis-icon"
            :name="isExpandHide ? 'hticon-vis-collapse-all' : 'hticon-vis-expand-all'"
          ></ht-icon>
          <q-tooltip> {{ isExpandHide ? '收起全部' : '展开全部' }} </q-tooltip>
        </q-btn>
      </div>
    </div>
    <!-- 筛选框 -->
    <div class="vis-store-page-container-header" v-show="isSearchHide">
      <div class="vis-store-page-container-header-search">
        <!-- clearable @clear="filterRef?.focus()" -->
        <q-input
          class="vis-store-page-container-header-input"
          ref="filterRef"
          rounded
          dense
          borderless
          placeholder="搜索图层名称"
          v-model="keyWord"
          @update:model-value="onKeyWordChange"
          @blur="onUpdateSearchHide"
        >
          <template #prepend>
            <ht-icon class="text-16px" name="hticon-vis-search"></ht-icon>
          </template>
        </q-input>
        <div class="vis-store-page-container-btn-group">
          <q-btn class="vis-store-page-container-btn" flat @click="onCloseSearch">
            <ht-icon class="vis-icon" name="hticon-vis-close"></ht-icon>
          </q-btn>
        </div>
      </div>
    </div>
    <!-- 拖拽树 -->
    <q-scroll-area ref="scrollAreaRef" class="h-full px-3">
      <div :class="['vis-store-page-container-tree_layout', { 'is-dragging': isDragging }]">
        <div v-if="!layers.length && isSearchHide && keyWord" class="empty-content">没有找到匹配的结果</div>
        <vis-layer-item
          v-else
          :nodes="layers"
          @drag-start="onDragStart"
          @drag-end="onDragEnd"
          @drag-add="onDragAdd"
          @drag-move="onCheckMove"
          :drag-target-parent-id="dragTargetParentId"
          :selected-ids="selectedIds"
          @update:selected-ids="onUpdateSelected"
          :depth="depth"
          :expanded-map="expandedMap"
          @update:expanded-map="onUpdateExpandHide"
          :is-search-hide="isSearchHide"
          :is-editing-status="isEditingStatus"
          @context-menu="onOpenContextMenu"
        />
      </div>
    </q-scroll-area>
  </div>
</template>

<script lang="ts" src="./layer.ts"></script>
<style lang="scss" scoped src="./layer.scss"></style>
