<template>
  <div class="vis-tab-group overflow-hidden" :style="allStyle">
    <q-scroll-area
      ref="scrollRef"
      v-if="layout.scrollbar && (layout.resizeX === ResizeType.Fixed || layout.resizeY === ResizeType.Fixed)"
      class="fit"
    >
      <div :style="tabStyle" :class="block.stroke ? 'vis-tab-group-content' : ''">
        <div
          v-for="(item, idx) in data"
          :key="idx"
          :style="tabItemStyle(idx)"
          class="vis-tab__item position-relative overflow-hidden cursor-pointer"
          ref="tabRef"
          @mouseover="hoverIndex = idx"
          @mouseleave="hoverIndex = -1"
          @click="handleActive(idx)"
        >
          <template v-if="shouldShowIcon(idx, 'before')">
            <!-- 悬浮状态下的图标 -->
            <div v-if="isShowStatus(idx, 'hover')" class="overflow-hidden !rounded-none">
              <q-img
                v-if="isImage(idx)"
                :style="getIconStyle(idx, 'picture', 'hover')"
                :src="statusIconUrl(idx, 'hover')"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'hover')" :icon="getIcon(idx, 'hover')" />
            </div>
            <!-- 选中状态下的图标 -->
            <div v-else-if="isShowStatus(idx, 'active')" class="overflow-hidden !rounded-none">
              <q-img
                v-if="isImage(idx)"
                :style="getIconStyle(idx, 'picture', 'active')"
                :src="statusIconUrl(idx, 'active')"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'active')" :icon="getIcon(idx, 'active')" />
            </div>
            <!-- 普通状态下的图标 -->
            <div v-else class="overflow-hidden !rounded-none">
              <q-img
                v-if="tabOption.adorn.type === AdornType.Image"
                :style="getIconStyle(idx, 'picture')"
                :src="iconUrl"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle(idx, 'icon')" :icon="tabOption.adorn.icon" />
            </div>
          </template>
          <div
            class="overflow-hidden vis-tab__text position-relative"
            :style="tabContentStyle(idx)"
            ref="tabContentRef"
          >
            <span :style="getItemOverflowStyle(idx)" ref="marqueeRefs">{{ item.columns }}</span>
            <!-- 用于检测换行时，文本是否处于溢出状态 -->
            <span
              v-if="tabOption.style.overflow === 2"
              class="position-absolute top-0 left-0 opacity-0 whitespace-nowrap"
              ref="changLineRefs"
              >{{ item.columns }}</span
            >
            <template v-if="tabOption.style.overflow === 3 && textOverflowFlags[idx]">
              <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
              <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
            </template>
          </div>
          <template v-if="shouldShowIcon(idx, 'after')">
            <!-- 悬浮状态下的图标 -->
            <div v-if="isShowStatus(idx, 'hover')" class="overflow-hidden !rounded-none">
              <q-img
                v-if="isImage(idx)"
                :style="getIconStyle(idx, 'picture', 'hover')"
                :src="statusIconUrl(idx, 'hover')"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'hover')" :icon="getIcon(idx, 'hover')" />
            </div>
            <!-- 选中状态下的图标 -->
            <div v-else-if="isShowStatus(idx, 'active')" class="overflow-hidden !rounded-none">
              <q-img
                v-if="isImage(idx)"
                :style="getIconStyle(idx, 'picture', 'active')"
                :src="statusIconUrl(idx, 'active')"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'active')" :icon="getIcon(idx, 'active')" />
            </div>
            <!-- 普通状态下的图标 -->
            <div v-else class="overflow-hidden !rounded-none">
              <q-img
                v-if="tabOption.adorn.type === AdornType.Image"
                :style="getIconStyle(idx, 'picture')"
                :src="iconUrl"
              ></q-img>

              <vis-svg-icon v-else :style="getIconStyle(idx, 'icon')" :icon="tabOption.adorn.icon" />
            </div>
          </template>
        </div>
      </div>
    </q-scroll-area>
    <div class="h-full" :class="block.stroke ? 'vis-tab-content' : ''" v-else :style="tabStyle">
      <div
        v-for="(item, idx) in data"
        :key="idx"
        :style="tabItemStyle(idx)"
        class="vis-tab__item position-relative overflow-hidden cursor-pointer"
        ref="tabRef"
        @mouseover="hoverIndex = idx"
        @mouseleave="hoverIndex = -1"
        @click="handleActive(idx)"
      >
        <template v-if="shouldShowIcon(idx, 'before')">
          <!-- 悬浮状态下的图标 -->
          <div v-if="isShowStatus(idx, 'hover')" class="overflow-hidden !rounded-none">
            <q-img
              no-transition
              no-spinner
              v-if="isImage(idx)"
              :style="getIconStyle(idx, 'picture', 'hover')"
              :src="statusIconUrl(idx, 'hover')"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'hover')" :icon="getIcon(idx, 'hover')" />
          </div>
          <!-- 选中状态下的图标 -->
          <div v-else-if="isShowStatus(idx, 'active')" class="overflow-hidden !rounded-none">
            <q-img
              no-transition
              no-spinner
              v-if="isImage(idx)"
              :style="getIconStyle(idx, 'picture', 'active')"
              :src="statusIconUrl(idx, 'active')"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'active')" :icon="getIcon(idx, 'active')" />
          </div>
          <!-- 普通状态下的图标 -->
          <div v-else class="overflow-hidden !rounded-none">
            <!-- :class="{ 'display-flex': tabOption.adorn.type === AdornType.Icon }" -->
            <q-img
              no-transition
              no-spinner
              v-if="tabOption.adorn.type === AdornType.Image"
              :style="getIconStyle(idx, 'picture')"
              :src="iconUrl"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle(idx, 'icon')" :icon="tabOption.adorn.icon" />
          </div>
        </template>
        <div
          v-if="getFontShow(idx)"
          class="overflow-hidden vis-tab__text position-relative"
          :style="tabContentStyle(idx)"
          ref="tabContentRef"
        >
          <span :style="getItemOverflowStyle(idx)" ref="marqueeRefs">{{ item.columns }}</span>
          <!-- 用于检测换行时，文本是否处于溢出状态 -->
          <span
            v-if="tabOption.style.overflow === 2"
            class="position-absolute top-0 left-0 opacity-0 whitespace-nowrap"
            ref="changLineRefs"
            >{{ item.columns }}</span
          >
          <template v-if="tabOption.style.overflow === 3 && textOverflowFlags[idx]">
            <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
            <span :style="getItemOverflowStyle(idx)">{{ item.columns }}</span>
          </template>
        </div>
        <template v-if="shouldShowIcon(idx, 'after')">
          <!-- 悬浮状态下的图标 -->
          <div v-if="isShowStatus(idx, 'hover')" class="overflow-hidden !rounded-none">
            <q-img
              no-transition
              no-spinner
              v-if="isImage(idx)"
              :style="getIconStyle(idx, 'picture', 'hover')"
              :src="statusIconUrl(idx, 'hover')"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'hover')" :icon="getIcon(idx, 'hover')" />
          </div>
          <!-- 选中状态下的图标 -->
          <div v-else-if="isShowStatus(idx, 'active')" class="overflow-hidden !rounded-none">
            <q-img
              no-transition
              no-spinner
              v-if="isImage(idx)"
              :style="getIconStyle(idx, 'picture', 'active')"
              :src="statusIconUrl(idx, 'active')"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle(idx, 'icon', 'active')" :icon="getIcon(idx, 'active')" />
          </div>
          <!-- 普通状态下的图标 -->
          <div v-else class="overflow-hidden !rounded-none">
            <q-img
              no-transition
              no-spinner
              v-if="tabOption.adorn.type === AdornType.Image"
              :style="getIconStyle(idx, 'picture')"
              :src="iconUrl"
            ></q-img>

            <vis-svg-icon v-else :style="getIconStyle(idx, 'icon')" :icon="tabOption.adorn.icon" />
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" src="./tab-group.ts"></script>
<style lang="scss" src="./tab-group.scss"></style>
