import { defineComponent, ref, watch, computed } from 'vue';
import { useActionStore } from '../../stores';
import { DesignAction } from '../../models';
import { Keyboard, getKeyText } from '@hetu/platform-shared';

export default defineComponent({
  name: 'vis-design-shortcuts',
  setup() {
    // #region 面板显示/隐藏
    const shortcutsRef = ref();
    const { actions, shortcuts, shortcutMenus } = useActionStore();

    const isPanelShow = ref(actions.value.shortcuts.active);
    const onUpdatePanelShow = (value: boolean) => (actions.value.shortcuts.active = value);
    watch(
      () => actions.value.shortcuts.active,
      () => {
        isPanelShow.value = actions.value.shortcuts.active;
      }
    );

    const onHide = () => {
      isPanelShow.value = false;
      tab.value = shortcuts.value.basic.name;
    };
    // #endregion

    // #region tab切换
    const tab = ref(shortcuts.value.basic.name);
    const onChangeTab = (value: string) => (tab.value = value);

    const getDisplayText = (keyboard: Keyboard | string) => {
      return getKeyText(keyboard);
    };

    // 处理特殊的快捷键
    const handleSpecialKey = (action: DesignAction) => {
      let shortcuts: (Keyboard | string)[] = [];
      const isMoveSpecials = [
        actions.value.moveSmallSteps.name,
        actions.value.moveBigSteps.name,
        actions.value.resizeGraphSmallSteps.name,
        actions.value.resizeGraphBigSteps.name
      ].includes(action.name);
      if (isMoveSpecials) {
        action.shortcuts.forEach((keyboard) => {
          Array.isArray(keyboard) ? shortcuts.push(...keyboard) : shortcuts.push(keyboard);
        });
        // 去重
        shortcuts = Array.from(new Set(shortcuts));
        // 处理方向键
        const Arrow = [Keyboard.ArrowUp, Keyboard.ArrowDown, Keyboard.ArrowLeft, Keyboard.ArrowRight];
        shortcuts = shortcuts.filter((item) => !Arrow.includes(item as Keyboard));
        shortcuts.push('方向键');
      }

      const isOpacity = [actions.value.setGraphOpacity.name].includes(action.name);
      isOpacity && (shortcuts = [Keyboard.Digit1, '~', Keyboard.Digit0]);

      return {
        isSpecial: isMoveSpecials || isOpacity,
        shortcuts: shortcuts
      };
    };
    // #endregion

    // #region 拖拽
    const isDragging = ref(false);
    const position = ref<{ x: number; y: number }>({ x: 0, y: 0 });
    const mouseStartPos = ref<{ x: number; y: number }>({ x: 0, y: 0 });
    const dragStartPos = ref<{ x: number; y: number }>({ x: 0, y: 0 });
    const dragStart = (event: MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();

      isDragging.value = true;
      mouseStartPos.value = { x: event.clientX, y: event.clientY };
      dragStartPos.value = { x: position.value.x, y: position.value.y };

      document.addEventListener('mousemove', onDrag);
      document.addEventListener('mouseup', dragEnd);
    };

    const onDrag = (event: MouseEvent) => {
      if (!isDragging.value) return;

      event.preventDefault();

      // 计算鼠标移动距离
      const deltaX = event.clientX - mouseStartPos.value.x;
      const deltaY = event.clientY - mouseStartPos.value.y;

      // 更新拖拽位置
      const newX = dragStartPos.value.x + deltaX;
      const newY = dragStartPos.value.y + deltaY;

      // 更新位置
      position.value = getBounds(newX, newY);
    };

    const dragEnd = () => {
      isDragging.value = false;

      // 移除事件监听器
      document.removeEventListener('mousemove', onDrag);
      document.removeEventListener('mouseup', dragEnd);
    };

    const SHORTCUT_WIDTH = 900; // 快捷键面板宽度
    const SHORTCUT_HEIGHT = 350; // 快捷键面板高度
    const HORIZONTAL_MIN_DISTANCE = 40; // 快捷键面板水平最小距离
    const TOP_MIN_DISTANCE = 40; // 快捷键面板顶部最小距离
    let cacheSize = { width: 0, height: 0 };
    const getBounds = (newX: number, newY: number) => {
      if (cacheSize.width !== window.innerWidth || cacheSize.height !== window.innerHeight) {
        cacheSize = {
          width: window.innerWidth,
          height: window.innerHeight
        };
      }

      const { width, height } = cacheSize;

      const minX = -SHORTCUT_WIDTH + HORIZONTAL_MIN_DISTANCE;
      const maxX = width - HORIZONTAL_MIN_DISTANCE;
      const minY = 0;
      const maxY = height - TOP_MIN_DISTANCE;

      // 边界限制
      const boundedX = Math.max(minX, Math.min(maxX, newX));
      const boundedY = Math.max(minY, Math.min(maxY, newY));

      return { x: boundedX, y: boundedY };
    };

    const shortcutStyle = computed(() => {
      return `
      width: ${SHORTCUT_WIDTH}px;
      height: ${SHORTCUT_HEIGHT}px;
      top: ${position.value.y}px; 
      left: ${position.value.x}px;
      `;
    });

    const DOCK_HEIGHT = 60; // dock高度
    const initPos = () => {
      const top = window.innerHeight - SHORTCUT_HEIGHT - DOCK_HEIGHT;
      const left = (window.innerWidth - SHORTCUT_WIDTH) / 2;
      position.value = { x: left, y: top };
    };

    watch(
      () => isPanelShow.value,
      (val) => {
        val && initPos();
      },
      { immediate: true }
    );
    // #endregion

    return {
      shortcutsRef,
      isPanelShow,
      onUpdatePanelShow,
      onHide,
      shortcuts,
      shortcutMenus,
      tab,
      onChangeTab,
      actions,
      getDisplayText,
      handleSpecialKey,

      isDragging,
      position,
      dragStart,
      shortcutStyle
    };
  }
});
