<template>
  <div class="vis-font-size">
    <vis-number ref="sizeRef" v-model="computedModel" :min="1" :class="{ 'no-icon': !icon }">
      <template #icon>
        <ht-icon v-if="icon" class="vis-icon drag-icon" :name="icon" />
        <div v-else class="drag-icon fit"></div>
      </template>
      <template #append>
        <q-icon :name="showMenu ? 'keyboard_arrow_up' : 'keyboard_arrow_down'" class="!text-xs cursor-pointer" />
        <q-menu
          v-model="showMenu"
          class="vis-menu min-w-20"
          dense
          anchor="bottom end"
          self="top end"
          @before-show="handleBeforeShow"
          :style="`width: ${menuWidth}px;`"
        >
          <q-list dense>
            <q-item
              v-for="item in fontSizeOptions"
              :key="item"
              clickable
              v-close-popup
              :active="item === computedModel"
              @click="changeFontSize(item)"
            >
              <q-item-section>{{ item }}</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </template>
    </vis-number>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
