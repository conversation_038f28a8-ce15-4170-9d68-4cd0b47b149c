import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { AroundPosition, useFill, type RangeOptions } from '@vis/document-core';
import { VisFormLabel, VisFormFix, VisFormSize } from '../common';
import { useWidget } from '../../../../../../../hooks';
import { useDesignStore } from '../../../../../../../stores';

export default defineComponent({
  name: 'vis-config-range-option',
  components: {
    VisFormSize,
    VisFormLabel,
    VisFormFix
  },
  props: {
    options: {
      type: Object as PropType<RangeOptions>,
      required: true
    }
  },
  setup(props) {
    const { handleManage } = useWidget();
    const { getFillStyle } = useFill();
    const designStore = useDesignStore();

    const computedOptions = computed(() => props.options);
    const thumbLabel = computed(() => computedOptions.value.thumbLabel);

    const labelManage = () => {
      handleManage(computedOptions.value, 'label');

      if (computedOptions.value.label) {
        computedOptions.value.label.text = '区间滑块';
      }
    };

    const activeGraph = computed(() => designStore.active.value.graph);
    const trackMaxHeight = computed(() => {
      return parseInt(`${activeGraph.value!.height / 2}`);
    });
    // 改变尺寸时，设置数值滑块高度
    watch(
      () => activeGraph.value!.height,
      (val) => {
        computedOptions.value.trackHeight = Math.max(4, Math.min(computedOptions.value.trackHeight, val / 2));
      }
    );

    const labelTextStyle = computed(() => {
      const textColor = getFillStyle(computedOptions.value.thumbLabel.textFillPaints);
      return textColor;
    });

    const popupRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      e.stopPropagation();
      popupShow.value = true;
      popupRef.value?.handleShow(e);
    };

    const positionOptions = [
      { label: '上', value: AroundPosition.Top },
      { label: '右', value: AroundPosition.Right }
    ];

    return {
      computedOptions,

      thumbLabel,

      labelManage,

      trackMaxHeight,

      labelTextStyle,
      popupRef,
      popupShow,
      showPopup,

      positionOptions
    };
  }
});
