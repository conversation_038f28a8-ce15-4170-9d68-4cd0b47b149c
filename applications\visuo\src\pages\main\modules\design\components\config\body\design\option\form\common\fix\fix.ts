import { computed, defineComponent, ref, type PropType } from 'vue';
import { FormFix } from '@vis/document-core';
import VisFormFixItem from './item/item.vue';
/**
 * 前后缀面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-form-fix',
  components: {
    VisFormFixItem
  },
  props: {
    title: {
      type: String
    },
    prefix: {
      type: Object as PropType<FormFix[]>,
      required: true
    },
    suffix: {
      type: Object as PropType<FormFix[]>,
      required: true
    },
    fixList: {
      type: Array as PropType<boolean[]>,
      default: () => [true, true, true, true]
    }
  },
  setup(props) {
    const computedPrefix = computed({
      get() {
        return props.prefix;
      },
      set(value) {
        Object.assign(props.prefix, value);
      }
    });

    const computedSuffix = computed({
      get() {
        return props.suffix;
      },
      set(value) {
        Object.assign(props.suffix, value);
      }
    });

    const addShow = computed(() => {
      const fixLength = props.fixList.filter((item) => item).length;
      return computedPrefix.value.length + computedSuffix.value.length < fixLength;
    });

    const showMenuFix = ref(false);
    /**
     * 添加前后缀（图标在前，文本在后）
     * @param value
     */
    const addFix = (fixType: 'prefix' | 'suffix', type: 'text' | 'icon') => {
      if (fixType === 'prefix') {
        type === 'icon'
          ? computedPrefix.value.unshift(new FormFix(type))
          : computedPrefix.value.push(new FormFix(type));
      }
      if (fixType === 'suffix') {
        type === 'icon'
          ? computedSuffix.value.unshift(new FormFix(type))
          : computedSuffix.value.push(new FormFix(type));
      }
    };

    const hasFix = (fixType: 'prefix' | 'suffix', type: 'text' | 'icon') => {
      if (fixType === 'prefix') {
        return computedPrefix.value.some((item) => item.type === type);
      }
      if (fixType === 'suffix') {
        return computedSuffix.value.some((item) => item.type === type);
      }
    };
    /**
     * 删除前后缀
     * @param value
     */
    const delFix = (fixType: 'prefix' | 'suffix', type: 'text' | 'icon') => {
      if (fixType === 'prefix') {
        computedPrefix.value.splice(
          computedPrefix.value.findIndex((item) => item.type === type),
          1
        );
      }
      if (fixType === 'suffix') {
        computedSuffix.value.splice(
          computedSuffix.value.findIndex((item) => item.type === type),
          1
        );
      }
      showMenuFix.value = false;
    };

    return {
      computedPrefix,
      computedSuffix,
      addShow,

      showMenuFix,
      addFix,
      hasFix,
      delFix
    };
  }
});
