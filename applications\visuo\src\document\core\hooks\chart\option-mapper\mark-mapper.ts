import type { Mark, BarMark, ChartOptions, FillPaints } from '../../../models';
import { rgbaToColor, getColorValue, mapFontStyle, mapConfig, getLineDashFromStroke } from './option-utils';

/**
 * 标记配置映射表
 * <AUTHOR>
 */
const MARK_MAPPING = {
  opacity: { source: 'opacity', default: 1 },

  // 填充样式
  fill: {
    source: 'fillPaints',
    transform: (fillPaints: any, config: any, options: any, seriesIndex?: number) => {
      // 单系列情况：使用全局 fillPaints
      if (options.mark.series.length === 1) {
        return getColorValue(options.mark.fillPaints);
      }

      // 多系列情况：根据系列索引获取对应颜色
      if (seriesIndex !== undefined && options.mark.series[seriesIndex]) {
        return getColorValue(options.mark.series[seriesIndex].fillPaints);
      }

      return getColorValue(fillPaints);
    }
  },
  fillOpacity: {
    source: 'fillPaints',
    transform: (fillPaints: any) => fillPaints?.color?.a || 1
  },

  // 描边样式
  stroke: {
    source: 'stroke.fillPaints',
    transform: (fillPaints: FillPaints, config: any, options: ChartOptions, seriesIndex?: number) => {
      // 单系列情况：使用全局 fillPaints
      if (options?.mark?.series.length === 1) {
        return getColorValue(options.mark.stroke.fillPaints);
      }

      // 多系列情况：根据系列索引获取对应颜色
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return getColorValue(options.mark.series[seriesIndex].stroke.fillPaints);
      }

      return getColorValue(fillPaints);
    }
  },
  strokeOpacity: {
    source: 'stroke.fillPaints.color.a',
    transform: (a: number, config: any, options: ChartOptions, seriesIndex?: number) => {
      // 单系列情况：使用全局配置
      if (options?.mark?.series.length === 1) {
        return options.mark.stroke.fillPaints?.color?.a || 1;
      }

      // 多系列情况：根据系列索引获取对应透明度
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return options.mark.series[seriesIndex].stroke.fillPaints?.color?.a || 1;
      }

      return a || 1;
    }
  },
  lineWidth: {
    source: 'stroke.position.0',
    transform: (width: number, config: any, options: ChartOptions, seriesIndex?: number) => {
      // 单系列情况：使用全局配置
      if (options?.mark?.series.length === 1) {
        return options.mark.stroke.position?.[0] || 0;
      }

      // 多系列情况：根据系列索引获取对应线宽
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return options.mark.series[seriesIndex].stroke.position?.[0] || 0;
      }

      return width || 1;
    }
  },
  lineDash: {
    source: 'stroke',
    transform: (stroke: any, config: any, options: ChartOptions, seriesIndex?: number) => {
      // 单系列情况：使用全局配置
      if (options?.mark?.series.length === 1) {
        return getLineDashFromStroke(options.mark.stroke);
      }

      // 多系列情况：根据系列索引获取对应线型
      if (seriesIndex !== undefined && options?.mark?.series[seriesIndex]) {
        return getLineDashFromStroke(options.mark.series[seriesIndex].stroke);
      }

      return getLineDashFromStroke(stroke);
    }
  },

  // 阴影效果
  shadowBlur: {
    source: 'effects.blur',
    default: 4,
    condition: (config: any) => config.effects?.visible && config.effects?.type === 'outset'
  },
  shadowColor: {
    source: 'effects.color',
    transform: (color: any, config: any) =>
      config.effects?.visible && config.effects?.type === 'outset' ? rgbaToColor(color) : undefined
  },
  shadowOffsetX: {
    source: 'effects.offset.x',
    default: 0,
    condition: (config: any) => config.effects?.visible && config.effects?.type === 'outset'
  },
  shadowOffsetY: {
    source: 'effects.offset.y',
    default: 4,
    condition: (config: any) => config.effects?.visible && config.effects?.type === 'outset'
  }
} as const;

/**
 * 标记配置映射器
 */
export const mapMarkConfig = (markConfig: Mark, options?: ChartOptions, seriesIndex?: number): Record<string, any> => {
  const markOptions: any = {};

  // 映射所有配置
  mapConfig(markConfig, markOptions, MARK_MAPPING, options, seriesIndex);

  return markOptions;
};

/**
 * 应用图形图样式配置
 */
export const applyMarkStyle = (mark: any, styleConfig: any) => {
  if (!mark || !styleConfig) {
    return;
  }

  try {
    // 应用基础样式
    if (styleConfig.opacity !== undefined) {
      mark.style('opacity', styleConfig.opacity);
    }

    if (styleConfig.fill) {
      mark.style('fill', styleConfig.fill);
    }

    if (styleConfig.stroke) {
      mark.style('stroke', styleConfig.stroke);
    }

    if (styleConfig.strokeWidth !== undefined) {
      mark.style('strokeWidth', styleConfig.strokeWidth);
    }

    if (styleConfig.fillOpacity !== undefined) {
      mark.style('fillOpacity', styleConfig.fillOpacity);
    }

    if (styleConfig.strokeOpacity !== undefined) {
      mark.style('strokeOpacity', styleConfig.strokeOpacity);
    }

    if (styleConfig.lineWidth !== undefined) {
      mark.style('lineWidth', styleConfig.lineWidth);
    }

    if (styleConfig.lineDash !== undefined) {
      mark.style('lineDash', styleConfig.lineDash);
    }

    // 应用阴影效果
    if (styleConfig.shadowBlur !== undefined) {
      mark.style('shadowBlur', styleConfig.shadowBlur);
    }

    if (styleConfig.shadowColor) {
      mark.style('shadowColor', styleConfig.shadowColor);
    }

    if (styleConfig.shadowOffsetX !== undefined) {
      mark.style('shadowOffsetX', styleConfig.shadowOffsetX);
    }

    if (styleConfig.shadowOffsetY !== undefined) {
      mark.style('shadowOffsetY', styleConfig.shadowOffsetY);
    }
  } catch (error) {
    console.error('应用图形样式配置失败:', error);
    throw error;
  }
};
