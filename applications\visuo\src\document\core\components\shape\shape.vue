<template>
  <svg
    v-if="shape.shapeType !== ShapeType.Rectangle"
    :width="shape.width + effectWidth * 4"
    :height="shape.height + effectWidth * 4"
    :viewBox="`0 0 ${shape.width + effectWidth * 4} ${shape.height + effectWidth * 4}`"
    aria-hidden="true"
    :style="{ transform: `translate(-${effectWidth * 2}px, -${effectWidth * 2}px)` }"
    preserveAspectRatio="xMidYMid meet"
  >
    <defs>
      <template v-for="(fill, i) in shape.fillPaints" :key="i">
        <linearGradient
          v-if="fill.type === FillType.Linear"
          :id="`${shape.id}-fill-${FillType.Linear}-${i}`"
          :gradientTransform="`rotate(${fill.rotation ? fill.rotation - 90 : 0}, 0.5, 0.5)`"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="0%"
        >
          <stop
            v-for="(stop, j) in colorStop(fill.stops || [])"
            :key="j"
            :offset="`${stop.position * 100}%`"
            :stop-color="`rgba(${stop.color.r},${stop.color.g},${stop.color.b},${stop.color.a * (fill.opacity || 1)})`"
          />
        </linearGradient>
        <radialGradient
          v-if="fill.type === FillType.Radial"
          :id="`${shape.id}-fill-${FillType.Radial}-${i}`"
          cx="50%"
          cy="50%"
          r="70.7%"
        >
          <stop
            v-for="(stop, j) in colorStop(fill.stops || [])"
            :key="j"
            :offset="`${stop.position * 100}%`"
            :stop-color="`rgba(${stop.color.r},${stop.color.g},${stop.color.b},${stop.color.a * (fill.opacity || 1)})`"
          />
        </radialGradient>
        <pattern
          v-if="fill.type === FillType.Image"
          :id="`${shape.id}-fill-${FillType.Image}-${i}`"
          x="0"
          y="0"
          width="100%"
          height="100%"
          patternUnits="userSpaceOnUse"
        >
          <image
            v-if="fill.image"
            :href="getImageUrl(fill.image)"
            x="0"
            y="0"
            width="100%"
            height="100%"
            :preserveAspectRatio="getImageFit(fill.image.objectFit)"
          />
        </pattern>
      </template>
      <!-- 边框颜色 -->
      <template v-if="shape.stroke && shape.stroke.fillPaints">
        <linearGradient
          v-if="shape.stroke.fillPaints.type === FillType.Linear"
          :id="`${shape.id}-stroke-${FillType.Linear}`"
          :gradientTransform="`rotate(${
            shape.stroke.fillPaints.rotation ? shape.stroke.fillPaints.rotation - 90 : 0
          }, 0.5, 0.5)`"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="0%"
        >
          <stop
            v-for="(stop, j) in colorStop(shape.stroke.fillPaints.stops || [])"
            :key="j"
            :offset="`${stop.position * 100}%`"
            :stop-color="`rgba(${stop.color.r},${stop.color.g},${stop.color.b},${
              stop.color.a * (shape.stroke.fillPaints.opacity || 1)
            })`"
          />
        </linearGradient>
        <radialGradient
          v-if="shape.stroke.fillPaints.type === FillType.Radial"
          :id="`${shape.id}-stroke-${FillType.Radial}`"
          cx="50%"
          cy="50%"
          r="70.7%"
        >
          <stop
            v-for="(stop, j) in colorStop(shape.stroke.fillPaints.stops || [])"
            :key="j"
            :offset="`${stop.position * 100}%`"
            :stop-color="`rgba(${stop.color.r},${stop.color.g},${stop.color.b},${
              stop.color.a * (shape.stroke.fillPaints.opacity || 1)
            })`"
          />
        </radialGradient>
        <pattern
          v-if="shape.stroke.fillPaints.type === FillType.Image"
          :id="`${shape.id}-stroke-${FillType.Image}`"
          x="0"
          y="0"
          width="100%"
          height="100%"
          patternUnits="userSpaceOnUse"
        >
          <image
            v-if="shape.stroke.fillPaints.image"
            :href="getImageUrl(shape.stroke.fillPaints.image)"
            x="0"
            y="0"
            width="100%"
            height="100%"
            :preserveAspectRatio="getImageFit(shape.stroke.fillPaints.image.objectFit)"
          />
        </pattern>
      </template>
      <!-- 特效 -->
      <template v-if="shape.effects">
        <filter :id="`${shape.id}-effects`" x="-200%" y="-200%" width="400%" height="400%">
          <feDropShadow
            v-if="shape.effects.type === EffectsType.Outset"
            :dx="shape.effects.offset.x"
            :dy="shape.effects.offset.y"
            :stdDeviation="shape.effects.blur"
            :flood-color="
              shape.effects.fillPaints && shape.effects.fillPaints.color
                ? `rgba(${shape.effects.fillPaints.color.r},${shape.effects.fillPaints.color.g},${shape.effects.fillPaints.color.b},${shape.effects.fillPaints.color.a})`
                : ''
            "
          />
          <feGaussianBlur v-if="shape.effects.type === EffectsType.Blur" :stdDeviation="shape.effects.blur" />
        </filter>
      </template>
    </defs>
    <!-- 有填充 -->
    <template v-if="shape.fillPaints && shape.fillPaints.length">
      <template v-for="(fill, i) in shape.fillPaints" :key="i">
        <path
          v-if="fill.visible"
          :stroke-width="shape.stroke ? shape.stroke.position[0] : 0"
          :d="path"
          :stroke="shape.stroke ? getFillPaints(shape.stroke?.fillPaints, i, 'stroke') : ''"
          :stroke-dasharray="shape.stroke ? dasharray(shape.stroke.style) : 0"
          :fill="getFillPaints(fill, i, 'fill')"
          :filter="`url(#${shape.id}-effects)`"
          stroke-linejoin="round"
          stroke-linecap="round"
          :transform="`translate(${effectWidth * 2}, ${effectWidth * 2})`"
        ></path>
      </template>
    </template>
    <!-- 无填充 -->
    <template v-else>
      <path
        :stroke-width="shape.stroke ? shape.stroke.position[0] : 0"
        :d="path"
        :stroke="shape.stroke ? getFillPaints(shape.stroke?.fillPaints, 0, 'stroke') : ''"
        :stroke-dasharray="shape.stroke ? dasharray(shape.stroke.style) : 0"
        fill="none"
        :filter="`url(#${shape.id}-effects)`"
        stroke-linejoin="round"
        stroke-linecap="round"
        clip-path="url(#irregularShape)"
        :transform="`translate(${effectWidth * 2}, ${effectWidth * 2})`"
      ></path>
    </template>
  </svg>
</template>
<script lang="ts" src="./shape.ts"></script>
<style lang="scss" src="./shape.scss"></style>
