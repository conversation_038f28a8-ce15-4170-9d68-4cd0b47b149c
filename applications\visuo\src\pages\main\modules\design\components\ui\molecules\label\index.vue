<template>
  <div class="vis-text relative">
    <!-- 字体 -->
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div v-if="title" class="vis-form-field__label">{{ title }}</div>
          <div class="vis-form-field__content">
            <vis-select v-model="computedModel.fontFamily" :options="fontFamilys" :popHeight="240">
              <template #option="{ opt, itemProps }">
                <q-item class="flex items-center" v-bind="itemProps">
                  <q-item-section>
                    <span :style="{ fontWeight: computedModel.fontWeight, fontFamily: opt }">{{ opt }}</span>
                  </q-item-section>
                </q-item>
              </template>
            </vis-select>
          </div>
        </div>
      </div>
      <!-- 显隐按钮 -->
      <q-btn v-if="!hideToggle" class="vis-field--mini btn-field" flat @click="handleVisible">
        <ht-icon class="vis-icon" :name="visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
      </q-btn>
    </div>

    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="flex-1">
          <!-- 粗细 -->
          <vis-select v-model="computedModel.fontWeight" :options="fontWeights">
            <template #option="{ opt, itemProps }">
              <q-item class="flex items-center" v-bind="itemProps">
                <q-item-section>
                  <span :style="{ fontWeight: opt.value, fontFamily: computedModel.fontFamily }">
                    {{ opt.label }}
                  </span>
                </q-item-section>
              </q-item>
            </template>
          </vis-select>
        </div>
        <div class="w-14">
          <!-- 字号-->
          <vis-font-size v-model="computedModel.fontSize" noIcon />
        </div>
        <div class="w-6">
          <!-- 颜色-->
          <vis-fill v-model="computedModel.fillPaints" :mini="true" :onlyColor="true" btnType="text" :showEyes="false">
          </vis-fill>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
