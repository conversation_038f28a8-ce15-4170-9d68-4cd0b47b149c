import { computed, defineComponent, ref, type PropType } from 'vue';
import { FontConfig, Font, useDocumentStore } from '@vis/document-core';

/**
 * 标签设置
 * <AUTHOR>
 */

export default defineComponent({
  name: 'vis-label',
  props: {
    modelValue: {
      type: Object as PropType<Font>,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    hideToggle: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const { fontWeights } = new FontConfig();

    const docStore = useDocumentStore();

    const fontFamilys = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });

    const computedModel = computed({
      get() {
        if (!props.modelValue) {
          return new Font();
        }
        return props.modelValue;
      },

      set(value) {
        Object.assign(props.modelValue, value);
      }
    });

    const visible = ref(computedModel.value.fillPaints.visible);
    const handleVisible = () => {
      visible.value = !visible.value;

      emit(
        'update:modelValue',
        Object.assign({}, computedModel.value, {
          fillPaints: { ...computedModel.value.fillPaints, visible: visible.value }
        })
      );
    };

    return {
      fontFamilys,
      fontWeights,
      computedModel,

      visible,
      handleVisible
    };
  }
});
