import { snapdom } from '@zumer/snapdom';

/**
 * DOM预处理选项
 */
interface PreprocessOptions {
  /** 是否移除不支持的元素 */
  removeUnsupportedElements?: boolean;
  /** 是否隐藏视频元素 */
  hideVideoElements?: boolean;
  /** 是否隐藏iframe元素 */
  hideIframeElements?: boolean;
  /** 是否移除脚本标签 */
  removeScripts?: boolean;
  /** 是否修复图片跨域问题 */
  fixCrossOriginImages?: boolean;
  /** 自定义元素选择器，用于移除特定元素 */
  removeSelectors?: string[];
  /** 自定义元素选择器，用于隐藏特定元素 */
  hideSelectors?: string[];
  /** 是否嵌入字体 */
  embedFonts?: boolean;
}

/**
 * 缩略图生成选项
 */
interface ThumbnailOptions extends PreprocessOptions {
  /** 截图质量 (0-1) */
  quality?: number;
  /** 是否启用快速模式 */
  fast?: boolean;
  /** 是否启用压缩 */
  compress?: boolean;
  /** 代理URL，用于处理跨域资源 */
  proxyUrl?: string;
}

/**
 * 克隆DOM元素并进行预处理
 * @param element 要克隆的DOM元素
 * @param options 预处理选项
 * @returns 克隆并预处理后的DOM元素
 */
function cloneAndPreprocessDOM(element: HTMLElement, options: PreprocessOptions = {}): HTMLElement {
  // 深度克隆元素
  const clonedElement = element.cloneNode(true) as HTMLElement;

  // 默认选项
  const {
    removeUnsupportedElements = true,
    hideVideoElements = true,
    hideIframeElements = true,
    removeScripts = true,
    fixCrossOriginImages = true,
    removeSelectors = [],
    hideSelectors = [],
  } = options;

  // 移除脚本标签
  if (removeScripts) {
    const scripts = clonedElement.querySelectorAll('script');
    scripts.forEach(script => script.remove());
  }

  // 移除不支持的元素
  if (removeUnsupportedElements) {
    const unsupportedSelectors = [
      'object',
      'embed',
      'applet',
      'audio',
      ...removeSelectors
    ];

    unsupportedSelectors.forEach(selector => {
      const elements = clonedElement.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });
  }

  // 隐藏视频元素
  if (hideVideoElements) {
    const videos = clonedElement.querySelectorAll('video');
    videos.forEach(video => {
      (video as HTMLElement).style.display = 'none';
    });
  }

  // 隐藏iframe元素
  if (hideIframeElements) {
    const iframes = clonedElement.querySelectorAll('iframe');
    iframes.forEach(iframe => {
      (iframe as HTMLElement).style.display = 'none';
    });
  }

  // 隐藏自定义选择器指定的元素
  hideSelectors.forEach(selector => {
    const elements = clonedElement.querySelectorAll(selector);
    elements.forEach(el => {
      (el as HTMLElement).style.display = 'none';
    });
  });

  // 修复图片跨域问题
  if (fixCrossOriginImages) {
    const images = clonedElement.querySelectorAll('img');
    images.forEach(img => {
      // 如果图片有跨域问题，尝试设置crossOrigin属性
      if (img.src && !img.src.startsWith(window.location.origin)) {
        img.crossOrigin = 'anonymous';
      }
    });
  }

  // 确保克隆的元素具有正确的样式
  const computedStyle = window.getComputedStyle(element);
  clonedElement.style.cssText = computedStyle.cssText;

  return clonedElement;
}

/**
 * 缩略图
 * @param coverWidth 缩略图宽
 * @param coverHeight 缩略图高
 * @param dom 要生成缩略图的dom元素
 * @param scale 缩放比例
 * @param options 额外选项
 * */
export const thumbnail = async (
  coverWidth: number,
  coverHeight: number,
  dom: HTMLElement,
  scale: number = 1,
  options: ThumbnailOptions = {}
): Promise<HTMLCanvasElement> => {
  try {
    // 验证输入参数
    if (!dom || !(dom instanceof HTMLElement)) {
      throw new Error('Invalid DOM element provided');
    }

    if (coverWidth <= 0 || coverHeight <= 0) {
      throw new Error('Cover width and height must be positive numbers');
    }

    if (scale <= 0) {
      throw new Error('Scale must be a positive number');
    }

    // 克隆并预处理DOM
    const clonedDOM = cloneAndPreprocessDOM(dom, options);

    // 将克隆的元素临时添加到文档中（用于样式计算）
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    tempContainer.style.visibility = 'hidden';
    tempContainer.appendChild(clonedDOM);
    document.body.appendChild(tempContainer);

    try {
      // 配置snapdom选项
      const snapdomOptions = {
        width: coverWidth,
        height: coverHeight,
        scale: scale,
        quality: options.quality || 1,
        fast: options.fast !== false, // 默认启用快速模式
        compress: options.compress !== false, // 默认启用压缩
        embedFonts: options.embedFonts || false,
        useProxy: options.proxyUrl || undefined,
      };

      // 使用snapdom生成截图
      const result = await snapdom(clonedDOM, snapdomOptions);

      // 转换为Canvas
      const canvas = await result.toCanvas();

      return canvas;

    } finally {
      // 清理临时元素
      document.body.removeChild(tempContainer);
    }

  } catch (error) {
    console.error('Thumbnail generation failed:', error);
    throw new Error(`Failed to generate thumbnail: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * 生成缩略图并转换为图片元素
 * @param coverWidth 缩略图宽
 * @param coverHeight 缩略图高
 * @param dom 要生成缩略图的dom元素
 * @param scale 缩放比例
 * @param options 额外选项
 * @returns Promise<HTMLImageElement>
 */
export const thumbnailToImage = async (
  coverWidth: number,
  coverHeight: number,
  dom: HTMLElement,
  scale: number = 1,
  options: ThumbnailOptions = {}
): Promise<HTMLImageElement> => {
  const canvas = await thumbnail(coverWidth, coverHeight, dom, scale, options);

  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (!blob) {
        reject(new Error('Failed to convert canvas to blob'));
        return;
      }

      const img = new Image();
      img.onload = () => {
        URL.revokeObjectURL(img.src);
        resolve(img);
      };
      img.onerror = () => {
        URL.revokeObjectURL(img.src);
        reject(new Error('Failed to load image from blob'));
      };

      img.src = URL.createObjectURL(blob);
    });
  });
};

/**
 * 生成缩略图并下载
 * @param coverWidth 缩略图宽
 * @param coverHeight 缩略图高
 * @param dom 要生成缩略图的dom元素
 * @param filename 文件名
 * @param scale 缩放比例
 * @param options 额外选项
 */
export const downloadThumbnail = async (
  coverWidth: number,
  coverHeight: number,
  dom: HTMLElement,
  filename: string = 'thumbnail',
  scale: number = 1,
  options: ThumbnailOptions & { format?: 'png' | 'jpg' | 'webp' } = {}
): Promise<void> => {
  try {
    const clonedDOM = cloneAndPreprocessDOM(dom, options);

    // 将克隆的元素临时添加到文档中
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    tempContainer.style.visibility = 'hidden';
    tempContainer.appendChild(clonedDOM);
    document.body.appendChild(tempContainer);

    try {
      const snapdomOptions = {
        width: coverWidth,
        height: coverHeight,
        scale: scale,
        quality: options.quality || 1,
        fast: options.fast !== false,
        compress: options.compress !== false,
        embedFonts: options.embedFonts || false,
        useProxy: options.proxyUrl || undefined,
      };

      const result = await snapdom(clonedDOM, snapdomOptions);

      // 使用snapdom的下载功能
      await result.download({
        format: options.format || 'png',
        filename: filename,
      });

    } finally {
      document.body.removeChild(tempContainer);
    }
  } catch (error) {
    console.error('Download thumbnail failed:', error);
    throw new Error(`Failed to download thumbnail: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * 预设的缩略图配置
 */
export const ThumbnailPresets = {
  /** 标准网页截图配置 */
  standard: {
    removeUnsupportedElements: true,
    hideVideoElements: true,
    hideIframeElements: true,
    removeScripts: true,
    fixCrossOriginImages: true,
    fast: true,
    compress: true,
    quality: 0.9,
  } as ThumbnailOptions,

  /** 高质量截图配置 */
  highQuality: {
    removeUnsupportedElements: true,
    hideVideoElements: true,
    hideIframeElements: true,
    removeScripts: true,
    fixCrossOriginImages: true,
    embedFonts: true,
    fast: false,
    compress: false,
    quality: 1,
  } as ThumbnailOptions,

  /** 快速截图配置 */
  fast: {
    removeUnsupportedElements: false,
    hideVideoElements: false,
    hideIframeElements: false,
    removeScripts: true,
    fixCrossOriginImages: false,
    fast: true,
    compress: true,
    quality: 0.7,
  } as ThumbnailOptions,
};

// 导出类型
export type { PreprocessOptions, ThumbnailOptions };

/**
 * 使用示例：
 *
 * // 基本用法
 * const element = document.getElementById('myElement');
 * const canvas = await thumbnail(800, 600, element, 1);
 * document.body.appendChild(canvas);
 *
 * // 使用预设配置
 * const canvas = await thumbnail(800, 600, element, 1, ThumbnailPresets.highQuality);
 *
 * // 自定义配置
 * const canvas = await thumbnail(800, 600, element, 1, {
 *   removeUnsupportedElements: true,
 *   hideVideoElements: true,
 *   removeSelectors: ['.ads', '.popup'],
 *   hideSelectors: ['.sidebar'],
 *   quality: 0.9,
 *   embedFonts: true
 * });
 *
 * // 生成图片元素
 * const img = await thumbnailToImage(400, 300, element);
 * document.body.appendChild(img);
 *
 * // 下载缩略图
 * await downloadThumbnail(800, 600, element, 'my-screenshot', 1, {
 *   format: 'png',
 *   quality: 1
 * });
 */
