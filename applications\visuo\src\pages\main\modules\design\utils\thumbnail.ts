import { snap } from '@zumerlab/snapdom';
/**
 * 缩略图
 * @param coverWidth 缩略图宽
 * @param coverHeight 缩略图高
 * @param dom 要生成缩略图的dom元素
 * @param scale 缩放比例
 * */
export const thumbnail = (coverWidth: number, coverHeight: number, dom: any, scale: number): Promise<HTMLCanvasElement> => {
  const { width, height } = dom.getBoundingClientRect();
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  canvas.width = coverWidth;
  canvas.height = coverHeight;
  ctx.drawImage(dom, 0, 0, width, height, 0, 0, coverWidth, coverHeight);
  return canvas;
};
