import type { BarMark } from '../../../models';
import { applyMarkStyle, mapMarkConfig } from './mark-mapper';
import { rgbaToColor, getColorValue, mapConfig } from './option-utils';

/**
 * 柱状图配置映射表
 * <AUTHOR>
 */
const BAR_MAPPING = {
  // 圆角配置
  radiusTopLeft: { source: 'radius.0', default: 0 },
  radiusTopRight: { source: 'radius.1', default: 0 },
  radiusBottomRight: { source: 'radius.2', default: 0 },
  radiusBottomLeft: { source: 'radius.3', default: 0 },

  // 柱子尺寸配置
  minHeight: { source: 'height', default: 20 },
  columnWidthRatio: {
    source: 'width',
    transform: (width: number) => (width > 0 ? Math.min(width / 100, 1) : 0.5)
  },
  insetBottom: { source: 'gap', default: 5 },

  // 背景配置
  background: {
    source: 'background',
    transform: (background: any) => {
      if (!background) return undefined;
      return {
        fill: background.fillPaints?.visible ? rgbaToColor(background.fillPaints.color) : undefined,
        fillOpacity: background.fillPaints?.color?.a || 1,
        stroke: getColorValue(background.stroke?.fillPaints),
        strokeWidth: background.stroke?.position?.[0] || 1,
        strokeOpacity: background.stroke?.fillPaints?.color?.a || 1
      };
    }
  },

  // 分组和堆叠配置
  groupGap: { source: 'groupGap', default: 0 },
  gap: { source: 'gap', default: 0 },
  showType: { source: 'showType', default: 'group' }
} as const;

/**
 * 柱状图配置映射器
 */
export const mapBarConfig = (barConfig: BarMark): Record<string, any> => {
  const barOptions: any = {};

  // 映射所有配置
  mapConfig(barConfig, barOptions, BAR_MAPPING);

  return barOptions;
};

/**
 * 应用柱状图样式配置
 */
export const applyBarStyle = (mark: any, styleConfig: any) => {
  if (!mark || !styleConfig) {
    return;
  }

  try {
    // 应用圆角样式
    if (styleConfig.radiusTopLeft !== undefined) {
      mark.style('radiusTopLeft', styleConfig.radiusTopLeft);
    }
    if (styleConfig.radiusTopRight !== undefined) {
      mark.style('radiusTopRight', styleConfig.radiusTopRight);
    }
    if (styleConfig.radiusBottomRight !== undefined) {
      mark.style('radiusBottomRight', styleConfig.radiusBottomRight);
    }
    if (styleConfig.radiusBottomLeft !== undefined) {
      mark.style('radiusBottomLeft', styleConfig.radiusBottomLeft);
    }

    // 应用尺寸样式
    if (styleConfig.minHeight !== undefined) {
      mark.style('minHeight', styleConfig.minHeight);
    }
    if (styleConfig.columnWidthRatio !== undefined) {
      mark.style('columnWidthRatio', styleConfig.columnWidthRatio);
    }
    if (styleConfig.insetBottom !== undefined) {
      mark.style('insetBottom', styleConfig.insetBottom);
    }

    // 应用背景样式
    if (styleConfig.background) {
      const bg = styleConfig.background;
      if (bg.fill) {
        mark.style('backgroundFill', bg.fill);
      }
      if (bg.fillOpacity !== undefined) {
        mark.style('backgroundFillOpacity', bg.fillOpacity);
      }
      if (bg.stroke) {
        mark.style('backgroundStroke', bg.stroke);
      }
      if (bg.strokeWidth !== undefined) {
        mark.style('backgroundStrokeWidth', bg.strokeWidth);
      }
      if (bg.strokeOpacity !== undefined) {
        mark.style('backgroundStrokeOpacity', bg.strokeOpacity);
      }
    }

    // 应用分组和堆叠配置
    if (styleConfig.groupGap !== undefined) {
      mark.style('groupGap', styleConfig.groupGap);
    }
    if (styleConfig.gap !== undefined) {
      mark.style('gap', styleConfig.gap);
    }
    if (styleConfig.showType) {
      mark.style('showType', styleConfig.showType);
    }
  } catch (error) {
    console.error('应用柱状图样式配置失败:', error);
    throw error;
  }
};
