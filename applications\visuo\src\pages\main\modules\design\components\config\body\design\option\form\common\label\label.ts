import { AroundPosition, type Label } from '@vis/document-core';
import { computed, defineComponent, nextTick, ref, type PropType } from 'vue';
import { useDesignStore } from '../../../../../../../../stores';
import { cloneDeep } from 'lodash-es';
/**
 * 表单通用标签属性面板
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-form-label',
  props: {
    options: {
      type: Object as PropType<Label>
    }
  },
  setup(props, { emit }) {
    const designStore = useDesignStore();

    const activeGraph = computed(() => designStore.active.value.graphs?.[0]);

    const toggleLabel = () => {
      const oldOption = cloneDeep(props.options);
      emit('handleManage', props.options, 'label');
      nextTick(() => {
        if (activeGraph.value) {
          if (props.options) {
            // 添加标签
            activeGraph.value.width = activeGraph.value.width + 50 + props.options.gap;
          } else {
            // 移除标签
            const { visible, fontStyle, gap, position, width } = oldOption || {};
            if (!visible) return; // 隐藏状态下删除，不重新计算

            if (position === AroundPosition.Left) {
              const w = `${width}`.includes('%')
                ? (activeGraph.value.width * parseInt(`${width}`)) / 100
                : Number(width);

              activeGraph.value.width = parseInt(`${activeGraph.value.width - w - Number(gap)}`);
            } else {
              activeGraph.value.height = parseInt(
                `${activeGraph.value.height - Number(fontStyle?.fontSize || 12) * 1.5 - Number(gap)}`
              );
            }
          }
        }
      });
    };

    /**
     * 设置标签显隐时，调整组件尺寸
     * @returns
     */
    const toggleVisible = () => {
      if (!computedOptions.value) return;
      computedOptions.value.visible = !computedOptions.value.visible;

      const { fontStyle, gap, position, width } = computedOptions.value;
      const contentPercent = `${width}`.includes('%') ? Number(`${width}`.replace('%', '')) / 100 : undefined;

      if (computedOptions.value.visible) {
        // 隐藏转显示
        if (position === AroundPosition.Left) {
          activeGraph.value.width = contentPercent
            ? parseInt(`${(activeGraph.value.width + Number(gap)) / contentPercent}`)
            : activeGraph.value.width + Number(gap) + Number(width);
        } else {
          activeGraph.value.height = parseInt(
            `${activeGraph.value.height + Number(fontStyle?.fontSize || 12) * 1.5 + Number(gap)}`
          );
        }
      } else {
        // 显示转隐藏
        if (position === AroundPosition.Left) {
          activeGraph.value.width = contentPercent
            ? parseInt(`${activeGraph.value.width * contentPercent - Number(gap)}`)
            : activeGraph.value.width - Number(gap) - Number(width);
        } else {
          activeGraph.value.height = parseInt(
            `${activeGraph.value.height - Number(fontStyle?.fontSize || 12) * 1.5 - Number(gap)}`
          );
        }
      }
    };

    const computedOptions = computed(() => props.options);

    const positionOptions = [
      {
        label: '左',
        value: AroundPosition.Left
      },
      {
        label: '上',
        value: AroundPosition.Top
      }
    ];

    const topPosition = computed(() => {
      return computedOptions.value?.position === AroundPosition.Top;
    });

    // 标签位置改变时，调整组件尺寸
    const handlePosition = (val: string) => {
      if (!computedOptions.value) return;

      const {
        width,
        gap,
        fontStyle: { fontSize }
      } = computedOptions.value;
      const labelHeight = fontSize * 1.5 + (computedOptions.value.gap || 0);
      const contentPercent = `${width}`.includes('%') ? Number(`${width}`.replace('%', '')) / 100 : undefined;

      if (val === AroundPosition.Top) {
        activeGraph.value.height += labelHeight;
        activeGraph.value.width = contentPercent
          ? parseInt(`${activeGraph.value.width * contentPercent - Number(gap)}`)
          : activeGraph.value.width - Number(width) - Number(gap);
      } else if (val === AroundPosition.Left) {
        const height = activeGraph.value.height - labelHeight;
        activeGraph.value.height = Math.max(20, height); // 高度不得小于20
        activeGraph.value.width = contentPercent
          ? parseInt(`${(activeGraph.value.width + Number(gap)) / contentPercent}`)
          : activeGraph.value.width + Number(gap) + Number(width);
      }
    };

    // #region 弹窗区域
    const popupRef = ref();
    const showPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.showPopup(e);
    };

    // #endregion
    return {
      toggleLabel,
      toggleVisible,

      computedOptions,
      positionOptions,
      topPosition,
      handlePosition,

      popupRef,
      showPopup
    };
  }
});
