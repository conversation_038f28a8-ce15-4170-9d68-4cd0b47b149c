<template>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span> 标签 </span>
      <div>
        <q-btn v-if="computedOptions" flat dense @click="toggleVisible">
          <ht-icon class="vis-icon" :name="computedOptions.visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
        </q-btn>
        <q-btn flat dense @click="toggleLabel">
          <ht-icon class="vis-icon" :name="computedOptions ? 'hticon-vis-subtract' : 'hticon-vis-add'" />
        </q-btn>
      </div>
    </div>
    <div v-if="computedOptions" class="vis-config-card__body">
      <div class="vis-form-label relative" :class="{ 'disabled pointer-events-none': !computedOptions.visible }">
        <!-- 内容 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <q-input
                  v-model="computedOptions.text"
                  placeholder="请输入标签内容"
                  flat
                  borderless
                  class="vis-input px-2 border-radius rounded-borders w-full"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 位置+对齐 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">位置</div>
              <div class="vis-form-field__content">
                <vis-button-group
                  v-model="computedOptions.position"
                  @update:modelValue="handlePosition"
                  :options="positionOptions"
                />
              </div>
            </div>
            <div class="vis-form-field">
              <div class="vis-form-field__label">间距</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.gap" icon="vis-max-height" :min="0" />
              </div>
            </div>
          </div>
        </div>

        <!-- 宽度 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">宽度</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="computedOptions.width"
                  icon="vis-resizing-w"
                  :min="0"
                  :disabled="topPosition"
                  percentage
                />
              </div>
            </div>
            <div class="vis-form-field"></div>
          </div>
        </div>

        <vis-text-more :text="computedOptions.fontStyle" ref="popupRef">
          <template #btn>
            <q-btn flat dense @click="showPopup" class="btn-field absolute top-0 right-0">
              <ht-icon class="vis-icon" name="vis-control" />
            </q-btn>
          </template>
        </vis-text-more>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./label.ts"></script>
