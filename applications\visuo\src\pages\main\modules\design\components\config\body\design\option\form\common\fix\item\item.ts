import { computed, defineComponent, ref, type PropType } from 'vue';
import { Icon, FormFix, useFill } from '@vis/document-core';
/**
 * 前后缀
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-form-fix-item',
  props: {
    options: {
      type: Object as PropType<FormFix>,
      required: true
    },
    type: {
      type: String as PropType<'text' | 'icon'>,
      required: true
    }
  },
  setup(props) {
    const { getFillStyle } = useFill();

    const computedOptions = computed({
      get() {
        return props.options;
      },
      set(value) {
        Object.assign(props.options, value);
      }
    });

    const isIcon = computed(() => props.type === 'icon');

    const handleIconChange = (icon: Icon) => {
      computedOptions.value.text = icon.name;
      computedOptions.value.icon = icon;
    };

    const handleVisible = () => {
      computedOptions.value.visible = !computedOptions.value.visible;
    };

    const textStyle = computed(() => {
      const textColor = getFillStyle(computedOptions.value.textFillPaints);
      return textColor;
    });

    /**
     * 图标样式
     */
    const iconStyle = computed(() => {
      if (!isIcon.value) return '';
      const iconColor = getFillStyle(computedOptions.value.icon.fillPaints);
      return {
        width: '12px',
        height: '12px',
        fill: iconColor.backgroundColor
      };
    });

    // #region 弹窗
    const popupRef = ref();
    const iconPickerRef = ref();
    const popupShow = ref(false);
    const showPopup = (e: Event) => {
      e.stopPropagation();
      popupShow.value = true;
      if (isIcon.value) {
        iconPickerRef.value?.showPopup(e);
      } else {
        popupRef.value?.handleShow(e);
      }
    };

    // #endregion

    return {
      computedOptions,
      isIcon,
      handleIconChange,

      handleVisible,

      textStyle,
      iconStyle,

      popupRef,
      showPopup,
      popupShow,
      iconPickerRef
    };
  }
});
