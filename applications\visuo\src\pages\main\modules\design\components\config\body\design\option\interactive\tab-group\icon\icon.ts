import { useMaterialDialog } from '@hetu/platform-app';
import { AttachmentService, isUUID } from '@hetu/platform-shared';
import { type Adorn, AdornType, AroundPosition, Image } from '@vis/document-core';
import { computed, defineComponent, onMounted, ref, watch, type PropType } from 'vue';

/**
 * 图标选择器组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-tab-icon-selector',
  props: {
    iconOption: {
      type: Object as PropType<Adorn>,
      required: true
    },
    minusWidth: {
      type: Number,
      default: 60
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  setup(props) {
    const iconPickerRef = ref();

    const popupShow = ref(false);

    const image = ref(props.iconOption.image || new Image());

    const imageUrl = ref('');

    const inputUrl = ref('');

    const showMenuType = ref(false);

    const iconOpt = computed(() => props.iconOption);

    const fixIconName = computed(
      () => props.iconOption.type === AdornType.Icon && props.iconOption.icon && props.iconOption.icon.name
    );

    const fixPicUrl = computed(() => props.iconOption.type === AdornType.Image && props.iconOption.image?.url);

    //#region 图标位置设置
    const positionIndex = ref(AroundPosition.Left);

    const positionIcon = ref('border-l');

    const positionOptions = [
      { label: '顶部', icon: 'border-t', value: AroundPosition.Top, active: false },
      { label: '底部', icon: 'border-b', value: AroundPosition.Bottom, active: false },
      { label: '左侧', icon: 'border-l', value: AroundPosition.Left, active: false },
      { label: '右侧', icon: 'border-r', value: AroundPosition.Right, active: false }
    ];

    const handlePositionChange = (item: { icon: string; value: AroundPosition; active: boolean }) => {
      positionOptions.forEach((item) => (item.active = false));
      item.active = true;
      positionIndex.value = item.value;
      positionIcon.value = item.icon || 'border-a';
      // 修改值
      iconOpt.value.position = item.value;
    };

    //#endregion 图标位置设置

    /**
     * 图片变化时提交更新
     */
    watch(
      () => image.value.url,
      () => {
        const newurl = image.value.url;
        if (newurl && !isUUID(newurl)) {
          imageUrl.value = newurl;
          inputUrl.value = newurl;
        } else {
          imageUrl.value = AttachmentService.downloadFileUrl(newurl);
          inputUrl.value = newurl;
        }
        if (iconOpt.value) {
          iconOpt.value.image = image.value;
        }
      },
      {
        immediate: true
      }
    );

    const showPopup = (e: Event) => {
      e.stopPropagation();
      if (props.iconOption.type === AdornType.None) return;
      if (props.iconOption.type === AdornType.Icon) {
        popupShow.value = true;
        iconPickerRef.value?.showPopup(e);
      } else {
        // 图片选择器处理
        selectImage();
      }
    };

    const selectImage = async () => {
      const { HtMaterialDialog } = await useMaterialDialog();

      HtMaterialDialog(image.value.url, 'image').onOk((value) => {
        image.value.url = value;
      });
    };

    const onSetUrl = () => {
      image.value.url = inputUrl.value;
    };

    const showIconPicker = (e: Event) => {
      e.stopPropagation();
      iconPickerRef.value?.showPopup(e);
    };

    const handleType = (value: AdornType) => {
      iconOpt.value.type = value;
      showMenuType.value = false;
    };

    const toggleVisible = () => {
      iconOpt.value.visible = !iconOpt.value.visible;
    };

    onMounted(() => {
      const positionIndex = positionOptions.findIndex((item) => item.value === iconOpt.value.position);
      if (positionIndex !== -1) {
        positionIcon.value = positionOptions[positionIndex].icon;
        positionOptions[positionIndex].active = true;
      }
    });

    return {
      iconPickerRef,
      popupShow,
      fixIconName,
      fixPicUrl,
      imageUrl,
      inputUrl,
      showMenuType,
      iconOpt,
      AdornType,
      showPopup,
      onSetUrl,
      showIconPicker,
      handleType,

      positionIndex,
      positionIcon,
      positionOptions,
      handlePositionChange,
      toggleVisible
    };
  }
});
