import { computed, defineComponent, ref } from 'vue';
import { FontConfig } from '@vis/document-core';

export default defineComponent({
  name: 'vis-font-size',
  props: {
    modelValue: {
      type: Number,
      required: true
    },
    noIcon: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const computedModel = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        handleUpdate(value);
      }
    });

    const sizeRef = ref();

    const icon = computed(() => {
      return props.noIcon ? '' : 'hticon-vis-layer-textbox';
    });

    const handleUpdate = (val: number) => {
      if (val === props.modelValue) return;

      emit('update:modelValue', Number(val));
      emit('change', Number(val));
    };

    const showMenu = ref(false);
    const { fontSizes: fontSizeOptions } = new FontConfig();
    const changeFontSize = (val: number) => {
      handleUpdate(val);
      showMenu.value = false;
    };

    const menuWidth = ref(0);
    const handleBeforeShow = (e: Event) => {
      menuWidth.value = sizeRef.value.$el.getBoundingClientRect().width - 16;
    };

    return {
      computedModel,
      sizeRef,
      icon,

      showMenu,
      fontSizeOptions,
      changeFontSize,
      menuWidth,
      handleBeforeShow
    };
  }
});
