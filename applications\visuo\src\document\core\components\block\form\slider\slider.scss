// $color-silder-width: var(--track-height, 12px);

.#{$vis-prefix}-slider {
  .vis-form__content {

    // 滑块样式
    .q-slider {
      padding: 0 var(--track-height, 8px);

      &__inner {
        background: transparent;
      }

      // 滑块按钮
      &__thumb {
        width: calc(var(--track-height, 8px) * 2) !important;
        height: calc(var(--track-height, 8px) * 2) !important;

        path {
          display: none;
        }

        &-shape {
          transform: scale(1) !important;
          background: var(--thumb-background, initial);
          border-radius: var(--thumb-border-radius, initial);
          border-width: var(--thumb-border-width, initial);
          border-color: var(--thumb-border-color, initial);
          border-style: var(--thumb-border-style, initial);
          border-image: var(--thumb-border-image, initial);
          border-image-slice: var(--thumb-border-image-slice, initial);
          box-shadow: var(--thumb-box-shadow, initial);
        }
      }

      // 标签背景跟随按钮
      &__text-container {
        background: var(--thumb-label-background, initial);

        .q-slider__text {
          color: var(--thumb-label-color, initial);
        }
      }

      // 标签小三角颜色
      &__pin {

        &::before {
          border-top-color: var(--thumb-label-background, initial);
        }
      }

      &__focus-ring {
        display: none !important;
      }

      // 选中的滑块轨道背景色
      &__selection {
        background: transparent;

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: calc(var(--track-height, 8px) * -1);
          right: calc(var(--track-height, 8px) * -1);
          height: var(--track-height, 8px);
          border-radius: calc(var(--track-height, 8px) * 0.5);
          background: var(--selection-background, initial);
        }
      }

      // 滑块轨道背景色
      &__track {
        height: var(--track-height, 8px) !important;
        border-radius: var(--track-height, 8px) !important;
        background-size: 0 !important;
        background: transparent;

        &::before {
          content: '';
          display: block;
          position: absolute;
          left: calc(var(--track-height, 8px) * -1);
          right: calc(var(--track-height, 8px) * -1);
          height: var(--track-height, 8px);
          border-radius: calc(var(--track-height, 8px) * 0.5);
          background: var(--track-background, initial);
        }
      }
    }

    // 滑块标签
    .thumb-label {
      overflow: hidden;
      margin-left: 8px;
      border-radius: 4px;
      padding: 0 4px;
      background: var(--thumb-label-background, initial);
      color: var(--thumb-label-color, initial);
      font-size: var(--thumb-label-font-size, initial);
      flex-shrink: 0;
      white-space: nowrap;
    }
  }
}