import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import {
  Direction,
  FontConfig,
  TextAdapt,
  SeniorFont,
  useDocumentStore,
  JustifyAlign,
  Align
} from '@vis/document-core';

/**
 * 文字样式组件
 */
export default defineComponent({
  name: 'vis-text-popup-text',
  props: {
    options: {
      type: Object as PropType<SeniorFont>,
      required: true
    }
  },
  setup(props, { emit }) {
    const docStore = useDocumentStore();
    const computedText = computed({
      get() {
        return props.options;
      },
      set(value) {
        Object.assign(props.options, value);
      }
    });

    /**
     * 设置文字修饰--斜体、下划线和删除线
     * @param val
     * @returns
     */
    const setFontStyle = (val: 'italic' | 'underlined' | 'through') => {
      if (computedText.value[val] === undefined) {
        return;
      }
      computedText.value[val] = !computedText.value[val];

      // 下划线和删除线不能同时选中
      if (computedText.value[val]) {
        if (val === 'underlined') {
          computedText.value.through = false;
        } else if (val === 'through') {
          computedText.value.underlined = false;
        }
      }
    };

    const { fontWeights } = new FontConfig();
    const fontFamilys = computed(() => {
      const newFontFamilys = docStore.fontFamilys.value?.map((item) => {
        if (typeof item === 'string') {
          return item;
        }
        return (item as { name: string }).name;
      });
      return newFontFamilys?.length > 0 ? newFontFamilys : [];
    });
    // 对齐方式
    const alignHorizontalOptions = [
      { value: JustifyAlign.Start, label: '', icon: 'vis-text-left', tip: '左对齐' },
      { value: JustifyAlign.Center, label: '', icon: 'vis-text-center', tip: '居中对齐' },
      { value: JustifyAlign.End, label: '', icon: 'vis-text-right', tip: '右对齐' },
      { value: JustifyAlign.Between, label: '', icon: 'vis-text-justify', tip: '两端对齐' }
    ];

    // 垂直对齐
    const alignVerticalOptions = [
      { value: Align.Start, label: '', icon: 'vis-vertical_t', tip: '顶部对齐' },
      { value: Align.Center, label: '', icon: 'vis-vertical_c', tip: '居中对齐' },
      { value: Align.End, label: '', icon: 'vis-vertical_b', tip: '底部对齐' }
    ];

    // 展示模式
    const adaptOptions = [
      { value: TextAdapt.Single, label: '', icon: 'vis-single', tip: '单行模式' },
      { value: TextAdapt.Auto, label: '', icon: 'vis-auto', tip: '自动高度' },
      { value: TextAdapt.Fixed, label: '', icon: 'vis-fixed', tip: '固定宽高' },
      { value: TextAdapt.Ellipsis, label: '', icon: 'vis-ellipsis', tip: '省略文本' }
    ];

    // 设置斜体、下划线和删除线
    const fontStyles = [
      { value: 'italic', label: '', icon: 'vis-format_italic', tip: '斜体' },
      { value: 'underlined', label: '', icon: 'vis-format_underline', tip: '下划线' },
      { value: 'through', label: '', icon: 'vis-format_through', tip: '删除线' }
    ];

    // 文本方向
    const textDirectionOptions = [
      {
        value: Direction.Horizontal,
        label: '',
        icon: `hticon-vis-layout-${Direction.Horizontal}`,
        tip: '水平'
      },
      {
        value: Direction.Vertical,
        label: '',
        icon: `hticon-vis-layout-${Direction.Vertical}`,
        tip: '垂直'
      }
    ];

    const fontStyleKeys: ('italic' | 'underlined' | 'through')[] = ['italic', 'underlined', 'through'];

    const handlerHoverOption = (val: string | boolean, label: string) => {
      isHover.value = val === false ? false : true;
      if (val) {
        if (label === 'fontStyle') {
          hoverOption.value.italic = val === 'italic';
          hoverOption.value.underlined = val === 'underlined';
          hoverOption.value.through = val === 'through';
        } else {
          (hoverOption.value[label as keyof SeniorFont] as string) = val as string;
        }
      } else {
        hoverOption.value = JSON.parse(JSON.stringify(computedText.value)) as SeniorFont;
      }
    };

    const isIconFont = (name: string) => {
      return name.startsWith('vis');
    };

    const isHover = ref(false);
    const hoverOption = ref<SeniorFont>(JSON.parse(JSON.stringify(computedText.value)));

    watch(
      () => computedText.value,
      (val) => {
        emit('change', val);
      },
      {
        deep: true
      }
    );
    return {
      computedText,
      setFontStyle,

      fontWeights,
      fontFamilys,
      alignHorizontalOptions,
      alignVerticalOptions,
      adaptOptions,
      fontStyles,
      textDirectionOptions,
      fontStyleKeys,
      isIconFont,
      isHover,
      hoverOption,
      handlerHoverOption
    };
  }
});
