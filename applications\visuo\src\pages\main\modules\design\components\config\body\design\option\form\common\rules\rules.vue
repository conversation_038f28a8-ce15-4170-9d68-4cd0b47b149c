<template>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span> {{ title || '校验规则' }} </span>
      <q-btn flat dense v-if="!hasAllRule">
        <ht-icon class="vis-icon" name="vis-add" />
        <q-menu v-model="showMenuRule" style="width: 90px" class="vis-menu" dense>
          <q-list dense>
            <q-item
              v-for="(rule, index) in addOptions"
              :key="index"
              :disable="hasRules(rule.value)"
              @click="addRule(rule.value)"
              clickable
              v-close-popup
            >
              <q-item-section>{{ rule.label }}</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>
    <div class="vis-config-card__body">
      <div v-for="(rule, index) in computedOptions" :key="index">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <template v-if="rule.type === FormRuleType.Length">
              <div class="vis-form-field">
                <div class="vis-form-field__label">最小长度</div>
                <div class="vis-form-field__content">
                  <vis-number
                    icon="hticon-vis-min"
                    v-if="Array.isArray(rule.pattern)"
                    v-model="rule.pattern[0]"
                    :min="0"
                  />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__label">最大长度</div>
                <div class="vis-form-field__content">
                  <vis-number
                    icon="hticon-vis-max"
                    v-if="Array.isArray(rule.pattern)"
                    v-model="rule.pattern[1]"
                    :min="0"
                  />
                </div>
              </div>
            </template>
            <template v-else-if="rule.type === FormRuleType.Range">
              <div class="vis-form-field">
                <div class="vis-form-field__label">最小值</div>
                <div class="vis-form-field__content">
                  <vis-number icon="hticon-vis-min" v-if="Array.isArray(rule.pattern)" v-model="rule.pattern[0]" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__label">最大值</div>
                <div class="vis-form-field__content">
                  <vis-number icon="hticon-vis-max" v-if="Array.isArray(rule.pattern)" v-model="rule.pattern[1]" />
                </div>
              </div>
            </template>
            <template v-else-if="rule.type === FormRuleType.Required">
              <q-checkbox :model-value="true" label="必填项" @update:model-value="delRule(index)" />
            </template>
            <div v-else class="vis-form-field">
              <div class="vis-form-field__label">模式</div>
              <div class="vis-form-field__content">
                <vis-select
                  v-model="rule.type"
                  @update:model-value="handleRule(rule)"
                  :options="patternList"
                  :minusWidth="0"
                />
              </div>
            </div>
          </div>
          <q-btn class="btn-field" @click="delRule(index)">
            <ht-icon class="vis-icon" name="vis-subtract" />
          </q-btn>
        </div>
        <div class="vis-form-inline" v-if="rule.type === FormRuleType.Pattern && isString(rule.pattern)">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <q-input
                  v-model="rule.pattern"
                  placeholder="正则表达式"
                  flat
                  borderless
                  class="vis-input px-2 border-radius rounded-borders w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./rules.ts"></script>
