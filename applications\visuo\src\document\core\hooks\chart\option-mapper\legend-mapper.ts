import type { ChartOptions, Legend } from '../../../models';
import { positionMap, directionMap, layoutJustifyMap, mapConfig, getColorValue } from './option-utils';

/**
 * 图例配置映射表
 * <AUTHOR>
 */
const LEGEND_MAPPING = {
  // 基础配置
  legend: {
    source: 'visible',
    transform: (visible: boolean) => (visible ? {} : false),
    fallback: false
  },
  'legend.color': {
    source: 'visible',
    transform: (visible: boolean) => (visible ? {} : false),
    fallback: false
  },
  'legend.color.position': {
    source: 'position',
    transform: (position: string) => positionMap[position] || 'top'
  },
  'legend.color.orientation': {
    source: 'direction',
    transform: (direction: string) => directionMap[direction] || 'horizontal'
  },

  // 布局配置
  'legend.color.layout': {
    source: 'position',
    transform: (position: string) => {
      const justifyContent = layoutJustifyMap[position] || 'center';
      return {
        justifyContent
      };
    }
  },

  // 离散图例配置 - 宽度配置
  /* 'legend.color.length': {
    source: 'discrete.width',
    transform: (width: number, config: any) => {
      const direction = config.direction || 'horizontal';
      const position = config.position || 'top';
      const isVerticalPosition = position.includes('left') || position.includes('right');

      // 垂直排列时，设置合适的宽度避免分页
      if (direction === 'vertical' && isVerticalPosition) {
        return width || 200; // 设置合适的宽度
      }
      return width;
    },
    condition: (config: any) => config.type === 'discrete' && config.discrete
  }, */

  'legend.color.itemWidth': {
    source: 'discrete.symbolGap',
    condition: (config: any) => config.type === 'discrete' && config.discrete,
    transform: (symbolGap: number, config: any) => {
      return symbolGap + config.discrete.symbolSize + config.discrete.labelSize;
    }
  },
  'legend.color.itemSpan': {
    source: 'discrete.symbolGap',
    condition: (config: any) => config.type === 'discrete' && config.discrete,
    transform: (symbolGap: number, config: any) => {
      return [1, 1, 0];
    }
  },
  'legend.color.itemSpacing': {
    source: 'discrete.symbolGap',
    condition: (config: any) => config.type === 'discrete' && config.discrete,
    transform: (symbolGap: number, config: any) => {
      return [symbolGap, 0];
    }
  },
  'legend.color.itemMarkerSize': {
    source: 'discrete.symbolSize',
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },

  // 控制图例项排列方向 - 通过cols控制
  'legend.color.cols': {
    source: 'discrete.columns',
    transform: (cols: number, config: any) => {
      const direction = config.direction || 'horizontal';
      return direction === 'vertical' ? 1 : '';
    },
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },

  // 水平排列图例特殊配置 - 控制行数
  'legend.color.maxRows': {
    source: 'discrete.maxRows',
    transform: (maxRows: number, config: any) => {
      const direction = config.direction || 'horizontal';
      const position = config.position || 'top';
      const isHorizontalPosition = position.includes('top') || position.includes('bottom');
      // 只有水平排列且上下位置的图例才需要maxRows配置
      return direction === 'horizontal' && isHorizontalPosition ? maxRows || 3 : undefined;
    },
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },

  // 垂直排列图例特殊配置 - 控制行间距
  'legend.color.rowPadding': {
    source: 'discrete.rowGap',
    transform: (rowGap: number, config: any) => {
      const direction = config.direction || 'horizontal';
      return direction === 'vertical' ? rowGap || 8 : undefined;
    },
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },
  // 水平排列图例特殊配置 - 控制列间距
  'legend.color.colPadding': {
    source: 'discrete.columnGap',
    transform: (columnGap: number, config: any) => {
      const direction = config.direction || 'horizontal';
      return direction === 'horizontal' ? columnGap || 8 : undefined;
    },
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },

  // 连续图例配置
  'legend.color.gradient': {
    source: 'gradient',
    transform: (gradient: any) => {
      if (!gradient) return undefined;

      const colorConfig: any = {};

      // 尺寸配置
      if (gradient.width) {
        colorConfig.width = gradient.width;
      }
      if (gradient.height) {
        colorConfig.height = gradient.height;
      }

      return colorConfig;
    },
    condition: (config: any) => config.type === 'gradient' && config.gradient
  },

  // 字体样式配置
  'legend.color.itemLabelFontSize': {
    source: 'fontStyle.fontSize',
    default: 12,
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },
  'legend.color.itemLabelFontFamily': {
    source: 'fontStyle.fontFamily',
    default: '默认字体',
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },
  'legend.color.itemLabelFontWeight': {
    source: 'fontStyle.fontWeight',
    default: 400,
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },
  'legend.color.itemLabelFill': {
    source: 'fontStyle.fillPaints',
    transform: (value: any) => getColorValue(value),
    condition: (config: any) => config.type === 'discrete' && config.discrete
  },

  // 图例项颜色配置
  'legend.color.itemMarkerFill': {
    source: 'fontStyle.fillPaints',
    transform: (value: any, config: any, options: any) => {
      if (options.mark.series.length === 1) {
        return getColorValue(options.mark.fillPaints);
      }
      return (datum: any, index: any, data: any) => {
        return getColorValue(options.mark.series[index].fillPaints);
      };
    },
    condition: false
  },

  // 标题配置
  'legend.color.title': {
    source: 'title.text',
    default: '',
    condition: (config: any) => config.title?.visible
  },
  'legend.color.titleDx': { source: 'title.offsetX', default: 0 },
  'legend.color.titleDy': { source: 'title.offsetY', default: 0 },
  'legend.color.titleTextAlign': { source: 'title.position', default: 'center' },
  'legend.color.titleOpacity': { source: 'title.opacity', default: 1 },
  'legend.color.titleAngle': { source: 'title.angle', default: 0 },
  'legend.color.titleFontSize': { source: 'fontStyle.fontSize', default: 12 },
  'legend.color.titleFontFamily': { source: 'fontStyle.fontFamily', default: '默认字体' },
  'legend.color.titleFontWeight': { source: 'fontStyle.fontWeight', default: 400 },
  'legend.color.titleLineHeight': { source: 'fontStyle.lineHeight', default: 0 },
  'legend.color.titleFill': {
    source: 'fontStyle.fillPaints',
    transform: (value: any) => getColorValue(value)
  }
} as const;

/**
 * 图例配置映射器
 */
export const mapLegendConfig = (legendConfig: Legend, options?: ChartOptions): Record<string, any> => {
  if (!legendConfig.visible) {
    return { legend: false };
  }

  const legendOptions: any = {};

  // 映射所有配置
  mapConfig(legendConfig, legendOptions, LEGEND_MAPPING, options);

  return legendOptions;
};

/**
 * 应用图例配置
 */
export const applyLegendConfig = (view: any, legendConfig: any) => {
  if (!view) {
    throw new Error('图表视图未初始化');
  }

  try {
    if (legendConfig === false) {
      view.legend(false);
    } else if (legendConfig && Object.keys(legendConfig).length > 0) {
      view.legend(legendConfig);
    }
  } catch (error) {
    console.error('应用图例配置失败:', error);
    throw error;
  }
};
