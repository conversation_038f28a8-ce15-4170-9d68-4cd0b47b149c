<template>
  <div class="vis-config-slider-option">
    <!-- 基础设置 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 基础 </span>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 初始值 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">初始值</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="computedOptions.defaultValue[0]"
                  :min="computedOptions.min"
                  :max="computedOptions.defaultValue[1]"
                  icon="hticon-vis-max-height"
                />
              </div>
            </div>
            <!-- 步长 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">结束值</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="computedOptions.defaultValue[1]"
                  :min="computedOptions.defaultValue[0]"
                  :max="computedOptions.max"
                  icon="hticon-vis-max-height"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 最小值 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">最小值</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.min" :max="computedOptions.max" />
              </div>
            </div>
            <!-- 最大值 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">最大值</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.max" :min="computedOptions.min" />
              </div>
            </div>
          </div>
        </div>
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 步长 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">步长</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.step" :min="0" icon="hticon-vis-max-width" />
              </div>
            </div>
            <div class="vis-form-field"></div>
          </div>
        </div>
      </div>
    </div>
    <q-separator />

    <!-- 样式设置 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span> 样式 </span>
      </div>
      <div class="vis-config-card__body">
        <!-- 尺寸 -->
        <vis-form-size :option="computedOptions" />

        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 轨道颜色 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">滑轨</div>
              <div class="vis-form-field__content">
                <vis-fill v-model="computedOptions.trackFillPaints" :minusWidth="0" base :showEyes="false"></vis-fill>
              </div>
            </div>
            <!-- 轨道背景 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">滑轨背景</div>
              <div class="vis-form-field__content">
                <vis-fill v-model="computedOptions.fillPaints" :minusWidth="0" base :showEyes="false"></vis-fill>
              </div>
            </div>
          </div>
        </div>

        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 滑轨高度 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">滑轨高度</div>
              <div class="vis-form-field__content">
                <vis-number v-model="computedOptions.trackHeight" :min="4" :max="trackMaxHeight" />
              </div>
            </div>
            <!-- 滑块 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">滑块</div>
              <div class="vis-form-field__content">
                <vis-fill v-model="computedOptions.thumb.fillPaints" :minusWidth="0" base :showEyes="false">
                  <template #color>
                    <vis-surface
                      v-model:fillPaints="computedOptions.thumb.fillPaints"
                      v-model:stroke="computedOptions.thumb.stroke"
                      v-model:effects="computedOptions.thumb.effects"
                    />
                  </template>
                </vis-fill>
              </div>
            </div>
          </div>
        </div>

        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-0"></div>
        </div>

        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 滑块标签 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">滑块标签</div>
              <div class="vis-form-field__content">
                <div class="vis-fill__content">
                  <q-input
                    v-model="thumbLabel.suffix"
                    borderless
                    class="rounded-borders flex-1 cursor-pointer thumb-label"
                    placeholder="文本后缀"
                  >
                    <template #prepend>
                      <q-btn class="vis-field--mini" @click.stop="showPopup" :class="{ active: popupShow }">
                        <q-icon
                          class="cursor-pointer vis-fill__icon hticon-vis-rect"
                          :style="labelTextStyle"
                          size="14px"
                        ></q-icon>
                        <vis-popup ref="popupRef" title="滑块标签" :target="false" @hide="popupShow = false">
                          <vis-color-selector title="文本" v-model="thumbLabel.textFillPaints" isText class="!mb-2" />
                          <vis-custom-color v-model="thumbLabel.textFillPaints" colorType="text" :onlyColor="true" />

                          <q-separator class="!my-2" />

                          <vis-color-selector title="背景" v-model="thumbLabel.fillPaints" class="!mb-2" />
                          <vis-custom-color v-model="thumbLabel.fillPaints" colorType="fill" :onlyColor="true" />
                        </vis-popup>
                      </q-btn>
                    </template>
                  </q-input>
                </div>
              </div>
            </div>
            <div class="vis-form-field">
              <div class="vis-form-field__label">位置</div>
              <div class="vis-form-field__content">
                <vis-button-group v-model="thumbLabel.position" :options="positionOptions" />
              </div>
            </div>
          </div>
        </div>
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <!-- 字号 -->
            <div class="vis-form-field">
              <div class="vis-form-field__label">字号</div>
              <div class="vis-form-field__content">
                <vis-font-size v-model="computedOptions.fontStyle.fontSize" />
              </div>
            </div>
            <div class="vis-form-field"></div>
          </div>
        </div>
      </div>
    </div>
    <q-separator />

    <!-- 标签设置 -->
    <vis-form-label :options="options.label" @handleManage="labelManage" />
    <q-separator />

    <!-- 前后缀 -->
    <vis-form-fix :prefix="computedOptions.prefix" :suffix="computedOptions.suffix"></vis-form-fix>
  </div>
</template>
<script lang="ts" src="./range.ts"></script>
