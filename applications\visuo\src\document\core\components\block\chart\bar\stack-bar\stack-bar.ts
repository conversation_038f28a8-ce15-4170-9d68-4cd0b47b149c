import type { Block, Chart } from '../../../../../models';
import { computed, defineComponent, ref, type PropType, onMounted, watch } from 'vue';
import { useBase, useChart } from '../../../../../hooks';

/**
 * 堆叠柱状图
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-stack-bar',
  props: {
    widget: {
      type: Object as PropType<Chart>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props) {

    const {
      init,
      getWidgetFieldData,
      loadStaticData,
      getWidgetOptions,
      getWidgetStyle,
      fields,
      getTemplate,
      getWidgetData,
      widgetStore
    } = useBase();

    const widget = computed(() => props.widget);

    const widgetData = ref([]);

    const loadWidgetData = async () => {
      if (!widget.value?.datasetId) {
        console.warn('StackBarComponent: 没有数据集ID');
        return;
      }

      try {
        if (widget.value?.datasetType === 'static') {
          const res = await loadStaticData();
          if (res) {
            widgetData.value = res;
            console.log('StackBarComponent: 加载柱状图静态数据', widgetData.value);
            // 确保 widgetStore 中存在对应的 widget 对象
            if (!widgetStore.value[widget.value?.id]) {
              widgetStore.value[widget.value?.id] = {
                data: null,
                ready: false,
                vars: []
              };
            }
            widgetStore.value[widget.value?.id].data = widgetData.value;
          }
        } else {
          // 处理动态数据
          const res = await getWidgetFieldData(
            new Promise((resolve) => {
              setTimeout(() => {
                resolve(true);
              }, 1000);
            })
          );
          if (res) {
            widgetData.value = res;
            console.log('BarComponent: 加载柱状图动态数据', widgetData.value);
          }
        }
      } catch (error) {
        console.error('BarComponent: 加载数据失败', error);
      }
    };

    // 组件挂载时加载数据
    onMounted(() => {
      loadWidgetData();
    });

    // 监听 widget 变化，重新加载数据
    watch(
      () => widget.value?.datasetId,
      () => {
        loadWidgetData();
      },
      { deep: true }
    );

    return {
      loadWidgetData,
      widgetData
    };
  }
});
