import { <PERSON><PERSON><PERSON>, Block, WidgetBlock, Aggregator } from '../models';
import { computed, getCurrentInstance, type ComponentPublicInstance } from 'vue';

/**
 * 数据处理
 * <AUTHOR>
 */
export const useWidgetData = () => {
  /**
   * 组件实例
   */
  const instance = getCurrentInstance();

  /**
   * 获取组件实例代理
   */
  const childComponent = instance?.proxy as ComponentPublicInstance & {
    $props: { widget: WidgetBlock; block: Block };
  };

  const widget = childComponent.$props.widget || null;
  const block = childComponent.$props.block || null;

  /**
   * 所有字段
   */
  const fields = computed(() => {
    return [
      ...widget.dataMapping,
      ...widget.xField,
      ...widget.yField,
      ...widget.columns,
      ...widget.rows,
      ...widget.levels,
      ...widget.filters
    ];
  });

  /**
   * 排序字段
   */
  const sortFields = computed(() => {
    return fields.value.filter((field) => field.sortDir);
  });

  /**
   * 聚合计算字段
   */
  const aggregatorFields = computed(() => {
    return fields.value.filter((field) => field.aggregator);
  });

  /**
   * 处理静态数据
   */
  const handleStaticData = (data: Array<any>) => {
    return new Promise((resolve) => {
      const mappedData = data.map((item: any) => {
        const result: any = {};
        // 处理每个字段映射
        fields.value.forEach((axisField) => {
          if (axisField && axisField.fieldName) {
            const value = item[axisField.fieldName];
            // 使用 axisField.fieldName 作为返回数据的key
            result[axisField.fieldName] = value;
          }
        });

        return result;
      });

      const resultData = handleDataAggregation(mappedData);

      // 根据 startRow 和 rowsLimit 截取数据
      const slicedData = sliceDataByLimit(resultData);

      resolve(slicedData);
    });
  };

  /**
   * 处理数据聚合
   */
  const handleDataAggregation = (data: Array<any>) => {
    // 如果没有聚合字段，返回原始数据
    if (!aggregatorFields.value.length) {
      return data;
    }

    // 获取分组字段（除了聚合字段之外的其他字段）
    const groupFields = fields.value.filter((field) => !field.aggregator);

    // 按组进行聚合计算
    const aggregated = data.reduce((acc, curr) => {
      // 构建分组键
      const groupKey = groupFields.map((field) => `${field.fieldName}:${curr[field.fieldName || ''] || ''}`).join('|');

      if (!acc[groupKey]) {
        // 初始化分组
        acc[groupKey] = { ...curr };
        // 初始化所有聚合字段为0
        aggregatorFields.value.forEach((field) => {
          if (field.fieldName) {
            acc[groupKey][field.fieldName] = 0;
          }
        });
      }

      // 对每个聚合字段进行相应的聚合计算
      aggregatorFields.value.forEach((field) => {
        if (field.fieldName && field.aggregator) {
          const fieldName = field.fieldName;
          const currentValue = parseFloat(curr[fieldName] || '0') || 0;
          const existingValue = (acc[groupKey] as any)[fieldName] || 0;

          // 根据聚合类型进行相应的计算
          switch (field.aggregator) {
            case Aggregator.SUM:
              (acc[groupKey] as any)[fieldName] = existingValue + currentValue;
              break;
            case Aggregator.COUNT:
              (acc[groupKey] as any)[fieldName] = existingValue + 1;
              break;
            case Aggregator.MAX:
              (acc[groupKey] as any)[fieldName] = Math.max(existingValue, currentValue);
              break;
            case Aggregator.MIN:
              (acc[groupKey] as any)[fieldName] = Math.min(existingValue, currentValue);
              break;
            case Aggregator.AVG:
              // 对于平均值，我们需要存储总和和计数，最后再计算平均值
              if (!(acc[groupKey] as any)[`${fieldName}_count`]) {
                (acc[groupKey] as any)[`${fieldName}_count`] = 0;
                (acc[groupKey] as any)[`${fieldName}_sum`] = 0;
              }
              (acc[groupKey] as any)[`${fieldName}_count`] += 1;
              (acc[groupKey] as any)[`${fieldName}_sum`] += currentValue;
              break;
          }
        }
      });

      return acc;
    }, {});

    // 处理平均值计算
    Object.keys(aggregated).forEach((groupKey) => {
      aggregatorFields.value.forEach((field) => {
        if (field.fieldName && field.aggregator === Aggregator.AVG) {
          const count = aggregated[groupKey][`${field.fieldName}_count`];
          const sum = aggregated[groupKey][`${field.fieldName}_sum`];
          if (count > 0) {
            aggregated[groupKey][field.fieldName] = sum / count;
          }
          // 清理临时字段
          delete aggregated[groupKey][`${field.fieldName}_count`];
          delete aggregated[groupKey][`${field.fieldName}_sum`];
        }
      });
    });

    // 将结果转换为数组格式
    return Object.values(aggregated);
  };

  /**
   * 根据 startRow 和 rowsLimit 截取数据
   * @param data 原始数据
   * @returns 截取后的数据
   */
  const sliceDataByLimit = (data: Array<any>) => {
    if (!widget || !block) {
      return data;
    }

    const { startRow = 1, rowsLimit } = widget;

    // 如果没有设置 rowsLimit 或为 0，返回所有数据
    if (!rowsLimit || rowsLimit <= 0) {
      return data;
    }

    // 确保 startRow 不为负数
    const validStartRow = Math.max(1, startRow);

    // 截取数据
    return data.slice(validStartRow - 1, validStartRow + rowsLimit - 1);
  };

  const getTemplate = (showFields: AxisField[], content?: string) => {
    // 根据字段数量动态生成索引占位符模板
    const indexTemplate = Array.from({ length: showFields.length }, (_, index) => `{${index}}`).join('');
    // 使用生成的索引模板替换原始内容，或者直接使用索引模板
    return content && hasValidPlaceholders(content) ? content : indexTemplate;
  };

  /**
   * 检测字符串中是否包含有效的占位符
   * @param content 要检测的字符串
   * @returns 是否包含有效占位符
   */
  const hasValidPlaceholders = (content: string): boolean => {
    // 使用正则表达式匹配 {数字}、{变量名}、{} 等占位符格式
    const placeholderRegex = /\{[^}]*\}/g;
    const matches = content.match(placeholderRegex);

    if (!matches) {
      return false;
    }

    // 检查是否至少有一个有效的占位符
    return matches.some((match) => {
      const innerContent = match.slice(1, -1); // 去掉 { 和 }

      // 空占位符 {} 是无效的
      if (innerContent === '') {
        return false;
      }

      // 数字索引占位符 {0}, {1}, {2} 等是有效的
      if (/^\d+$/.test(innerContent)) {
        return true;
      }

      // 变量名占位符 {var_}, {fieldName} 等是有效的（包含字母、数字、下划线）
      if (/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(innerContent)) {
        return true;
      }

      // 带样式的占位符 {style|content} 是有效的
      if (innerContent.includes('|')) {
        return true;
      }

      return false;
    });
  };

  /**
   * 获取组件数据
   */
  const getWidgetData = (fields: AxisField[], data: any, template?: string) => {
    if (!template) {
      return '';
    }
    const match = spliceBraces(template);

    let result = template;

    // 遍历匹配到的字段名，替换模板中的占位符
    match.forEach((chart: string) => {
      const str = analysisChart(chart, fields, data);
      // str === 0 || str === false 处理变量值为0和false的情况
      result = result.replace('{' + chart + '}', str || str === 0 || str === false ? str : '');
    });

    return result;
  };

  /**
   * 解析单个{}中数据
   * @param chart 字段名或表达式
   * @param fields 字段数组
   * @param data 数据对象
   */
  const analysisChart = (chart: string, fields?: Array<AxisField>, data?: any) => {
    if (chart.indexOf('|') === -1) {
      // 没有样式分隔符，直接解析字段
      if (fields) {
        return analysisField(chart, fields, data);
      }
    } else {
      // 有样式分隔符，处理样式
      const index = chart.indexOf('|');
      const chartArr = [chart.slice(0, index), chart.slice(index + 1, chart.length)];
      const style = chartArr[0]; // 简化处理，直接使用样式字符串
      const _match = spliceBraces(chartArr[1]);

      if (!_match.length) {
        return '<span style="' + style + '">' + chartArr[1] + '</span>';
      } else {
        let content = chartArr[1];
        _match.forEach((m: string) => {
          const str = analysisChart(m, fields, data);
          content = content.replace('{' + m + '}', str || str === 0 || str === false ? str : '');
        });
        return '<span style="' + style + '">' + content + '</span>';
      }
    }
  };

  /**
   * 解析字段返回数据值
   * @param chart 字段名或索引
   * @param fields 字段数组
   * @param data 数据对象
   */
  const analysisField = (chart: string, fields: Array<AxisField>, data: any) => {
    // 检查是否为数字索引
    const filedIndex = Number.parseInt(chart, 10);
    if (!isNaN(filedIndex) && fields[filedIndex]) {
      // 使用索引访问字段
      const fieldName = fields[filedIndex].fieldName as string;
      return data[fieldName];
    } else {
      // 使用字段名或别名查找
      const field = fields.find((f) => f.fieldName === chart || f.fieldAlias === chart);
      if (field) {
        const dataKey = field.fieldName || field.fieldAlias || chart;
        return data[dataKey];
      } else {
        // 直接使用字段名从data中获取
        return data[chart];
      }
    }
  };

  /**
   * 匹配括号内容
   * @param template 模板字符串
   * @returns 匹配到的括号内容数组
   */
  const spliceBraces = (template: string): Array<string> => {
    const matches: Array<string> = [];
    let currentContent = '';
    let inBrace = false;

    for (let i = 0; i < template.length; i++) {
      const char = template[i];

      if (char === '{' && !inBrace) {
        // 开始新的括号内容
        inBrace = true;
        currentContent = '';
      } else if (char === '}' && inBrace) {
        // 找到完整的括号对
        matches.push(currentContent);
        inBrace = false;
        currentContent = '';
      } else if (inBrace) {
        // 在括号内，收集内容
        currentContent += char;
      }
    }

    return matches;
  };

  return {
    fields,
    sortFields,
    aggregatorFields,
    handleStaticData,
    getWidgetData,
    getTemplate
  };
};
