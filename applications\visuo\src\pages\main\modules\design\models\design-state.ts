import type { Records } from '@hetu/util';
import { Block, Frame, Graph, GridPosition, Page, useDocumentStore, WidgetBlock } from '@vis/document-core';
import { DesignAction } from '../models';

export class DesignActiveState {
  /** 当前选中的页面 */
  page: Page = new Page();
  /** 当前选中的容器: 当前选中图形的父Frame */
  frame?: Frame;
  /** 当前选中的图形 */
  graphs: Graph[] = [];
  /** 当前选中的图形id */
  graphIds: string[] = [];
  /** 当前选中的图形 */
  get graph() {
    return this.graphs.length ? this.graphs[0] : undefined;
  }
  get block() {
    const docStore = useDocumentStore();
    if (!this.graph || !(this.graph as Block)?.decoration) return undefined;
    return docStore.document.value.blocks.find((b) => b.id === (this.graph as Block)?.decoration) as WidgetBlock;
  }
}

/**
 * 容器状态
 */
export class FrameState {
  /** grid布局时：网格项大小和位置 */
  gridItemRect: Records<{ w: number; h: number; x: number; y: number }> = {};
  /** 布局网格：栅格行、栅格列的大小和位置 */
  layoutGuideGrids: Records<{ w: number; h: number; x: number; y: number }[]> = {};
}

/**
 * 标尺状态
 */
export class RulerState {
  /** 缩放范围 */
  zoomRange: number[] = [0.1, 2];
  /** 当前缩放比例 */
  zoom: number = 1;
  /** 标尺选中的范围 */
  selectedRangesH: number[][] = [];
  selectedRangesV: number[][] = [];
  /** 初始参考线基准位置 */
  defaultGuidesPos: number = 0;
  /** 初始滚动位置 */
  defaultScrollPos: number = 0;
  /** 水平位置 */
  horizontalPos: number = 20;
  /** 垂直位置 */
  verticalPos: number = 20;
}

/**
 * 画布状态
 */
export class CanvasState {
  /** 是否显示用于添加组件的容器 */
  dropbox: boolean = false;
  /** 当前正在旋转图形 */
  isRotate: boolean = false;
  /** 当前正在拖拽的图形id，用于在flex布局中，正在拖拽的图形脱离flex布局 */
  dragging: string[] = [];
  /** 当前正在调整大小的图形 id*/
  resizeing: string[] = [];
  /** 当前可放置的容器 1.从左侧面板拖拽组件时，记录组件放置在哪个容器内 */
  frame: Frame | undefined;
  /** activeFrame为grid布局时，记录拖拽过程中图形放置的格子位置 [row, col]*/
  gridRowCol: number[] | undefined;
  /** 存储平级的容器,用于计算当前鼠标在哪个容器内 */
  flattenFrames: Frame[] = [];
  /** 当前正在重命名的图形 id，用于记录当前编辑的页面/页面组/图形等节点的id */
  renaming: string | undefined = undefined;
  /** 当前正在复制的图形 id */
  clipboard: string[] = [];
  /** 快捷键 */
  shortcuts: Record<string, DesignAction> = {};
}

/**
 * 等比缩放
 */
export class RatioScale {
  width: number = 0;
  height: number = 0;
  scale: number = 100;
  position: GridPosition = GridPosition.Center;
}

/**
 * 面板状态
 */
export class PanelState {
  /** 左侧面板 */
  menu = MenuPanel.File;
  property = PropertyPanel.Design;
  /** 图形属性面板 */
  graph = GraphPanel.Style;
  /** 左右两侧宽度值，默认241px，1px的边框占位 */
  leftWidth = 241;
  rightWidth = 241;
}

export enum MenuPanel {
  File = 'file',
  Chart = 'chart',
  Control = 'control',
  Material = 'material'
}

export enum PropertyPanel {
  Design = 'design',
  Interactive = 'interactive'
}

export enum GraphPanel {
  Style = 'style',
  Data = 'data',
  Option = 'option'
}

export enum DesignerMode {
  /** 设计模式 */
  Design = 'design',
  /** 节点编程 */
  Node = 'node',
  /** 交互模式 */
  Interact = 'interact',
  /** 禅模式 */
  dhyana = 'dhyana'
}
