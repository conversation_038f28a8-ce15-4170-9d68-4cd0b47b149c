import {
  ResizeType,
  SelectMode,
  Adorn,
  AdornType,
  TabItemStyle,
  TabGroupOptions,
  iconPositionOptions,
  Carousel,
  DisplayDirection,
  FillType
} from '@vis/document-core';
import { computed, defineComponent, ref, watch, type PropType, type Ref } from 'vue';
import VisConfigTabStatus from './status/status.vue';
import VisTabIconSelector from './icon/icon.vue';
import VisTabSurface from './surface/surface.vue';
import { cloneDeep, isString } from 'lodash-es';

/**
 * <AUTHOR>
 * 选项卡组件属性面板
 */
export default defineComponent({
  name: 'vis-config-tab-group-option',
  components: {
    VisConfigTabStatus,
    VisTabIconSelector,
    VisTabSurface
  },
  props: {
    options: {
      type: Object as PropType<TabGroupOptions>,
      required: true
    }
  },
  setup(props) {
    const selectMode = ref(SelectMode.Single);

    const hoverItem = ref('text');

    const activeItem = ref('text');
    const tabOptions = computed(() => {
      return props.options;
    });
    const tabStyle = computed(() => props.options.style);

    const popupShow = ref(false);

    const selectOptions = [
      {
        value: SelectMode.Single,
        label: '单选'
      },
      {
        value: SelectMode.Multiple,
        label: '多选'
      }
    ];

    const overflowOptions = [
      {
        value: 0,
        label: '截断'
      },
      {
        value: 1,
        label: '省略号'
      },
      {
        value: 2,
        label: '换行'
      },
      {
        value: 3,
        label: '跑马灯'
      }
    ];

    const directionOptions = [
      {
        value: DisplayDirection.Horizontal,
        label: '',
        icon: `hticon-vis-layout-${DisplayDirection.Horizontal}`,
        tip: '水平'
      },
      {
        value: DisplayDirection.Vertical,
        label: '',
        icon: `hticon-vis-layout-${DisplayDirection.Vertical}`,
        tip: '垂直'
      },
      { value: DisplayDirection.Grid, label: '', icon: `hticon-vis-layout-${DisplayDirection.Grid}`, tip: '网格' }
    ];

    //#region  布局
    const popupRef = ref();
    const showMenuWidth = ref(false);
    const showMenuHeight = ref(false);

    const onShowPopup = (e: Event) => {
      e.stopPropagation();
      popupRef.value?.handleShow(e);
    };

    const onChangeResize = (type: 'resizeX' | 'resizeY', resize: ResizeType) => {
      tabOptions.value.layout[type] = resize;
    };

    const hoverGrid = ref([0, 0]);
    const onMouseMovePicker = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.className.indexOf('hand') !== -1) {
        const row = target.getAttribute('data-row');
        const col = target.getAttribute('data-col');
        hoverGrid.value = [Number(row), Number(col)];
      }
    };

    const onMouseLeavePicker = () => {
      hoverGrid.value = [0, 0];
    };

    const onClickGrid = (row: number, col: number) => {
      tabOptions.value.layout.row = row;
      tabOptions.value.layout.column = col;
    };

    /**
     * 行距显示条件
     */
    const getShowLineGutter = computed(
      () =>
        tabOptions.value.layout.direction === DisplayDirection.Vertical ||
        (tabOptions.value.layout.flowWrap &&
          tabOptions.value.layout.direction === DisplayDirection.Horizontal &&
          tabOptions.value.layout.resizeX === ResizeType.Fixed)
    );
    //#endregion 布局

    //#region --------- 圆角 ---------------

    const isMixed = computed(
      () => !tabOptions.value.style.radius.every((item) => item === tabOptions.value.style.radius[0])
    );

    const isLeftMixed = computed(() => tabOptions.value.style.radius[0] !== tabOptions.value.style.radius[3]);

    const isRightMixed = computed(() => tabOptions.value.style.radius[1] !== tabOptions.value.style.radius[2]);

    const allRadius: Ref<number | string> = ref(isMixed.value ? '多值' : props.options.style.radius[0]);
    const leftRadius: Ref<number | string> = ref(isLeftMixed.value ? '多值' : props.options.style.radius[0]);
    const rightRadius: Ref<number | string> = ref(isRightMixed.value ? '多值' : props.options.style.radius[1]);

    const showRadius = ref(false);

    const isAllRadius = computed(() => {
      // 垂直布局或 水平布局时只有宽度为自适应 或者 宽度为固定宽度换行未开启的状态下才显示
      return (
        tabOptions.value.layout.direction === DisplayDirection.Vertical ||
        (tabOptions.value.layout.direction === DisplayDirection.Horizontal &&
          (tabOptions.value.layout.resizeX === ResizeType.Adapt ||
            (!tabOptions.value.layout.flowWrap && tabOptions.value.layout.resizeX === ResizeType.Fixed)))
      );
    });

    const radiusChange = (val: number, position: string) => {
      if (position === 'all') {
        if (typeof allRadius.value === 'number') {
          tabOptions.value.style.radius = [val, val, val, val];
        }
      } else {
        if (position === 'left') {
          if (typeof leftRadius.value === 'number') {
            tabOptions.value.style.radius[0] = val;
            tabOptions.value.style.radius[3] = val;
          }
        } else {
          if (typeof rightRadius.value === 'number') {
            tabOptions.value.style.radius[1] = val;
            tabOptions.value.style.radius[2] = val;
          }
        }
      }
    };

    const callbackPlace = (val: string, type: 'all' | 'left' | 'right') => {
      if (isString(val) && isNaN(Number(val))) {
        if (type === 'all') {
          return !isMixed.value ? tabOptions.value.style.radius[0] : '多值';
        } else if (type === 'left') {
          return !isLeftMixed.value ? tabOptions.value.style.radius[0] : '多值';
        } else {
          return !isRightMixed.value ? tabOptions.value.style.radius[0] : '多值';
        }
      }
      return true;
    };

    watch(
      () => isMixed.value,
      (val) => {
        allRadius.value = val ? '多值' : tabOptions.value.style.radius[0];
      }
    );

    watch(
      () => showRadius.value,
      (val) => {
        if (!val) {
          if (!isAllRadius.value) {
            leftRadius.value = isLeftMixed.value ? '多值' : tabOptions.value.style.radius[0];
            rightRadius.value = isRightMixed.value ? '多值' : tabOptions.value.style.radius[1];
          }
        }
      }
    );

    watch(
      () => tabOptions.value.layout.direction,
      () => {
        if (isAllRadius.value) {
          showRadius.value = allRadius.value === '多值';
        }
      }
    );

    watch(
      () => isAllRadius.value,
      (val) => {
        if (val) {
          // 单个圆角设置
          showRadius.value = isMixed.value;
        } else {
          showRadius.value = isLeftMixed.value || isRightMixed.value;
        }
      },
      {
        immediate: true
      }
    );

    //#endregion --------- 圆角 ---------------

    const toggle = () => {
      if (tabOptions.value.carousel) {
        tabOptions.value.carousel = undefined;
      } else {
        tabOptions.value.carousel = new Carousel();
      }
    };

    //#region 图标
    const onAspectRatio = () => {
      tabOptions.value.adorn!.image.aspectRatio = !tabOptions.value.adorn!.image.aspectRatio;
    };

    const changeSize = (value: number, oldValue: number, type: 'width' | 'height') => {
      if (!isNaN(value) && !isNaN(oldValue) && tabOptions.value.adorn.image.aspectRatio) {
        const ratio = value / oldValue;
        if (type === 'width') {
          tabOptions.value.adorn.image.height = Math.round(tabOptions.value.adorn.image.height * ratio);
        } else {
          tabOptions.value.adorn.image.width = Math.round(tabOptions.value.adorn.image.width * ratio);
        }
      }
    };

    //#endregion 图标

    //#region ---------------- 状态 ----------------------

    // const changeStatus = (status: 'hover' | 'active') => {
    //   if (tabStyle.value[status]) {
    //     tabStyle.value[status] = undefined;
    //   } else {
    //     tabStyle.value[status] = new TabItemStyle();
    //   }
    // };

    const toggleStatus = (status: 'hover' | 'active', type: 'text' | 'adorn' | 'graph') => {
      if (type === 'text') {
        if (tabStyle.value[status].fontStyle) {
          tabStyle.value[status].fontStyle = undefined;
          tabStyle.value[status].textEffects = undefined;
        } else {
          const fontStyle = cloneDeep(tabStyle.value.fontStyle);
          delete fontStyle.direction;
          // delete fontStyle.alignHorizontal;
          // delete fontStyle.alignVertical;

          tabStyle.value[status].fontStyle = fontStyle;
          tabStyle.value[status].textEffects = cloneDeep(tabStyle.value.textEffects);
        }
      } else if (type === 'adorn') {
        tabStyle.value[status].adorn = tabStyle.value[status].adorn ? undefined : cloneDeep(tabOptions.value.adorn);
      } else {
        if (tabStyle.value[status].fillPaints) {
          tabStyle.value[status].fillPaints = undefined;
          tabStyle.value[status].stroke = undefined;
          tabStyle.value[status].effects = undefined;
        } else {
          tabStyle.value[status].fillPaints = cloneDeep(tabStyle.value.fillPaints);
          tabStyle.value[status].stroke = cloneDeep(tabStyle.value.stroke);
          tabStyle.value[status].effects = cloneDeep(tabStyle.value.effects);
        }
      }
    };

    //#endregion ---------------- 状态 ----------------------

    return {
      selectMode,
      tabOptions,
      tabStyle,
      selectOptions,
      SelectMode,
      overflowOptions,
      hoverItem,
      activeItem,
      ResizeType,
      showMenuWidth,
      showMenuHeight,
      allRadius,
      leftRadius,
      rightRadius,
      isAllRadius,
      showRadius,
      directionOptions,
      DisplayDirection,
      hoverGrid,
      getShowLineGutter,
      iconPositionOptions,
      popupRef,
      popupShow,
      AdornType,
      onChangeResize,
      radiusChange,
      callbackPlace,
      toggle,
      // changeStatus,
      onMouseMovePicker,
      onMouseLeavePicker,
      onClickGrid,
      onShowPopup,
      onAspectRatio,
      changeSize,
      toggleStatus,

      FillType
    };
  }
});
