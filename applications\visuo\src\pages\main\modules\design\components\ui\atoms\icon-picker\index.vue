<template>
  <vis-popup class="vis-icon-picker" title="图标" ref="popupRef" @hide="hidePopup" :target="false">
    <div class="vis-form-inline">
      <div class="vis-form-field row justify-between" style="gap: 8px">
        <div class="flex-1">
          <vis-fill v-model="color" :minusWidth="0" tooltip="颜色" :onlyColor="true" :showEyes="false" />
        </div>
        <div class="col-4">
          <vis-font-size v-model="computedModel.size" noIcon />
        </div>
      </div>

      <div class="vis-form-field" style="gap: 8px">
        <div class="flex-1">
          <div class="text-3 flex items-center cursor-pointer">
            <span>{{ iconTypeOptions.find((item) => item.value === iconType)?.label || '全部' }}</span>
            <q-icon :name="typeMenuShow ? 'keyboard_arrow_down' : 'keyboard_arrow_right'" class="vis-icon" />
          </div>
          <q-menu v-model="typeMenuShow" max-height="300px" class="vis-menu">
            <q-list dense>
              <q-item :active="!iconType" clickable @click="handleIconType('')">
                <q-item-section>全部</q-item-section>
              </q-item>
              <q-item
                v-for="type in iconTypeOptions"
                :key="type.value"
                :active="type.value === iconType"
                clickable
                @click="handleIconType(type.value)"
              >
                <q-item-section>{{ type.label }}</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </div>

        <div class="flex-1 flex justify-end">
          <q-input
            ref="filterRef"
            v-model="filterName"
            class="pr-2 rounded-borders"
            :class="filterActive ? 'vis-input w-full' : '!w-6'"
            flat
            dense
            borderless
            placeholder="查找"
            :debounce="300"
            clearable
            @blur="handleFilterBlur"
            @clear="handleFilterBlur"
          >
            <template #prepend>
              <q-btn flat dense @click="handleFilter">
                <q-icon name="search" class="vis-icon" />
              </q-btn>
            </template>
          </q-input>
        </div>
      </div>
    </div>
    <div class="vis-icon-picker__list">
      <q-scroll-area class="vis-scroll-area" style="height: 400px" :content-style="{ height: '400px' }">
        <template v-if="iconList.length">
          <div
            class="vis-icon"
            :class="{ active: computedModel.name === icon.name }"
            @click="handleSelect(icon)"
            v-for="icon in iconList"
            :key="icon.name"
          >
            <div class="flex flex-center w-full h-full">
              <vis-svg-icon v-if="icon.name" :icon="icon" lazy />
            </div>
            <q-tooltip :offset="[0, 4]">{{ icon.title }}</q-tooltip>
          </div>
        </template>
        <div v-else class="flex flex-center w-full h-full">列表为空</div>
      </q-scroll-area>
    </div>
  </vis-popup>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
