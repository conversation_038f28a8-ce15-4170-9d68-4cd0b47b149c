<template>
  <div class="vis-text-popup-text">
    <vis-preview :ishover="isHover" :option="hoverOption" />
    <div class="vis-form-inline">
      <div class="vis-form-field">
        <div class="vis-form-field__content">
          <vis-select v-model="computedText.fontFamily" :options="fontFamilys" :popHeight="240" class="w-full">
            <template #option="{ opt, itemProps }">
              <q-item class="flex items-center" v-bind="itemProps">
                <q-item-section>
                  <span :style="{ fontWeight: computedText.fontWeight, fontFamily: opt }">{{ opt }}</span>
                </q-item-section>
              </q-item>
            </template>
          </vis-select>
        </div>
      </div>

      <div class="vis-form-field gap-2">
        <!-- 粗细 -->
        <div class="flex-1">
          <vis-select v-model="computedText.fontWeight" :options="fontWeights">
            <template #option="{ opt, itemProps }">
              <q-item class="flex items-center" v-bind="itemProps">
                <q-item-section>
                  <span :style="{ fontWeight: opt.value, fontFamily: computedText.fontFamily }">
                    {{ opt.label }}
                  </span>
                </q-item-section>
              </q-item>
            </template>
          </vis-select>
        </div>

        <div class="w-14">
          <!-- 字号-->
          <vis-font-size v-model="computedText.fontSize" noIcon />
        </div>
        <div class="w-6">
          <!-- -->
          <vis-fill v-model="computedText.fillPaints" title="颜色" :mini="true" btnType="text" :noImage="true" />
        </div>
      </div>

      <!-- 字间距 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">字间距</div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedText.letterSpacing" icon="vis-resizing-w" :min="0" />
        </div>
      </div>
      <!-- 行高 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">行高</div>
        <div class="vis-form-field__content">
          <vis-number v-model="computedText.lineHeight" icon="vis-resizing-h" :min="0" />
        </div>
      </div>
      <!-- 对齐方式 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">对齐方式</div>
        <div class="vis-form-field__content">
          <vis-button-group
            v-model="computedText.alignHorizontal"
            :options="alignHorizontalOptions"
            @handleHover="handlerHoverOption($event, 'alignHorizontal')"
          ></vis-button-group>
        </div>
      </div>

      <!-- 垂直对齐 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">垂直对齐</div>
        <div class="vis-form-field__content">
          <vis-button-group
            v-model="computedText.alignVertical"
            :options="alignVerticalOptions"
            @handleHover="handlerHoverOption($event, 'alignVertical')"
          ></vis-button-group>
        </div>
      </div>

      <!-- 斜体、下划线、删除线 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">文字修饰</div>
        <div class="vis-form-field__content">
          <div class="flex-1 flex btn-bg">
            <q-btn
              v-for="(item, index) in fontStyles"
              :key="item.value"
              dense
              flat
              size="xs"
              class="rounded-borders overflow-hidden flex-1"
              :class="[
                { 'vis-btn-active': computedText[fontStyleKeys[index]] },
                { 'q-mr-xs': index === fontStyles.length - 1 }
              ]"
              :label="item.label"
              @click="setFontStyle(fontStyleKeys[index])"
              @mouseenter="handlerHoverOption(item.value, 'fontStyle')"
              @mouseleave="handlerHoverOption(false, 'fontStyle')"
            >
              <template v-if="item.icon">
                <ht-icon v-if="isIconFont(item.icon)" :name="item.icon" class="vis-icon" />
                <q-icon v-else :name="item.icon" />
              </template>
              <q-tooltip v-if="item.tip">{{ item.tip }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>

      <!-- 文字方向 -->
      <div class="vis-form-field" v-if="computedText.direction">
        <div class="vis-form-field__label">文本方向</div>
        <div class="vis-form-field__content">
          <vis-button-group
            v-model="computedText.direction"
            :options="textDirectionOptions"
            @handleHover="handlerHoverOption($event, 'direction')"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script src="./index.ts" lang="ts"></script>
<style src="./index.scss" lang="scss"></style>
