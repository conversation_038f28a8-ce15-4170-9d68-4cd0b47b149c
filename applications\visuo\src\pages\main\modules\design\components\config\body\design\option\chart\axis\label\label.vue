<template>
  <div class="vis-form-inline">
    <template v-if="type === 'yAxis'">
      <!-- 数值范围 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">数值范围</div>
        <div class="vis-form-field__content">
          <vis-mix-input v-model="minExtent" icon="hticon-vis-min"> </vis-mix-input>
          <vis-mix-input v-model="maxExtent" icon="hticon-vis-max"> </vis-mix-input>
        </div>
      </div>

      <!-- 对数刻度 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">对数刻度</div>
        <div class="vis-form-field__content justify-end">
          <q-toggle class="vis-toggle" v-model="options.logarithmic" />
        </div>
      </div>

      <!-- 反转坐标轴方向 -->
      <div class="vis-form-field">
        <div class="vis-form-field__label">反转 Y 轴</div>
        <div class="vis-form-field__content justify-end">
          <q-toggle class="vis-toggle" v-model="options.inverse" />
        </div>
      </div>
    </template>

    <!-- 两端间距 -->
    <div class="vis-form-field">
      <div class="vis-form-field__label">两端间距</div>
      <div class="vis-form-field__content">
        <vis-number v-model="options.spacing" icon="vis-padding-x" :min="0"> </vis-number>
      </div>
    </div>
    <!-- 轴线距离 -->
    <div class="vis-form-field">
      <div class="vis-form-field__label">轴线距离</div>
      <div class="vis-form-field__content">
        <vis-number v-model="options.label.gap" icon="vis-padding-top" :min="0" />
      </div>
    </div>
    <!-- 旋转角度 -->
    <div class="vis-form-field">
      <div class="vis-form-field__label">旋转角度</div>
      <div class="vis-form-field__content">
        <vis-number v-model="options.label.angle" icon="vis-rotate" :min="-90" :max="90" suffix="°" />
      </div>
    </div>
    <!-- 偏移 -->
    <div class="vis-form-field">
      <div class="vis-form-field__label">偏移</div>
      <div class="vis-form-field__content">
        <div class="vis-form-field--width-50">
          <div class="vis-form-field__content">
            <vis-number v-model="options.label.offsetX" icon="vis-letter-x" :min="0" />
          </div>
        </div>
        <div class="vis-form-field--width-50">
          <div class="vis-form-field__content">
            <vis-number v-model="options.label.offsetY" icon="vis-letter-y" :min="0" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./label.ts"></script>
