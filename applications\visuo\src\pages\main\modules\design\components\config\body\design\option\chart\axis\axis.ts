import { computed, defineComponent, ref, type PropType, watch } from 'vue';
import type { Axis } from '@vis/document-core';

/**
 * 图表坐标轴配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-chart-axis',
  props: {
    options: {
      type: Object as PropType<Axis>,
      required: true
    }
  },
  setup(props) {
    const axisOptions = computed(() => props.options);

    const handleTitleVisible = () => {
      axisOptions.value.title.visible = !axisOptions.value.title.visible;
    };

    const gridBackground = ref(false);

    watch(
      () => axisOptions.value.grid.stroke.position[0],
      (newVal) => {
        axisOptions.value.grid.width = newVal;
      }
    );

    watch(
      () => axisOptions.value.line.stroke.position[0],
      (newVal) => {
        axisOptions.value.line.width = newVal;
      }
    );

    watch(
      () => axisOptions.value.label.fontStyle.visible,
      (newVal) => {
        axisOptions.value.label.visible = newVal;
      }
    );

    return {
      axisOptions,
      handleTitleVisible,
      gridBackground
    };
  }
});
