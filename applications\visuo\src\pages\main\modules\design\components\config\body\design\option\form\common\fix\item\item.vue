<template>
  <div class="vis-config-fix-item vis-form-field">
    <div
      class="vis-fill__content flex flex-nowrap"
      :class="{ 'disabled pointer-events-none': !computedOptions.visible }"
    >
      <!-- 内容 -->
      <div v-if="isIcon" class="flex-1">
        <q-input
          :model-value="computedOptions.text || '选择图标'"
          @click="showPopup"
          borderless
          class="rounded-borders flex-1 pr-2 cursor-pointer"
          :input-class="['cursor-pointer', { '!text-gray-500': !computedOptions.text }]"
          readonly
        >
          <template #prepend>
            <div @click="showPopup">
              <q-btn class="vis-field--mini" :class="{ active: popupShow }">
                <vis-svg-icon v-if="computedOptions.text" :icon="computedOptions.icon" :style="iconStyle" />
                <ht-icon v-else name="hticon-vis-suffix" class="vis-icon" />
              </q-btn>
            </div>
          </template>
        </q-input>
        <vis-icon-picker
          ref="iconPickerRef"
          v-model="computedOptions.icon"
          @update:model-value="handleIconChange"
          @hide="popupShow = false"
          class="flex-1"
        ></vis-icon-picker>
      </div>
      <template v-else>
        <q-input
          v-model="computedOptions.text"
          borderless
          class="rounded-borders flex-1 pr-2 cursor-pointer"
          placeholder="文本内容"
        >
          <template #prepend>
            <q-btn class="vis-field--mini" @click.stop="showPopup" :class="{ active: popupShow }">
              <q-icon class="cursor-pointer vis-fill__icon hticon-vis-rect" :style="textStyle" size="14px"></q-icon>
              <vis-popup ref="popupRef" title="辅助" :target="false" @hide="popupShow = false">
                <vis-color-selector title="文本" v-model="computedOptions.textFillPaints" isText class="!mb-2" />
                <vis-custom-color v-model="computedOptions.textFillPaints" colorType="text" :onlyColor="true" />

                <q-separator class="!my-2" />

                <vis-color-selector title="背景" v-model="computedOptions.fillPaints" class="!mb-2" />
                <vis-custom-color v-model="computedOptions.fillPaints" colorType="fill" :onlyColor="true" />
              </vis-popup>
            </q-btn>
          </template>
        </q-input>
      </template>
      <q-separator vertical class="!m-0" />
    </div>
  </div>
  <q-btn @click="handleVisible">
    <ht-icon class="vis-icon" :name="computedOptions.visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
  </q-btn>
</template>
<script lang="ts" src="./item.ts"></script>
<style lang="scss" src="./item.scss"></style>
