import { BarMark, FillType, WidgetBlock } from '@vis/document-core';
import { useDesignStore } from '../../../../../../../../stores';
import { isString } from 'lodash-es';
import { defineComponent, ref, type PropType, computed, watch } from 'vue';

/**
 * 柱状图标记配置
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-bar',
  props: {
    options: {
      type: Object as PropType<BarMark>,
      required: true
    }
  },
  setup(props) {

    const computedOptions = computed(() => props.options);

    const showTypeOptions = [
      { value: 'group', label: '分组' },
      { value: 'stack', label: '堆叠' },
      { value: 'percent-stack', label: '百分比堆叠' }
    ];

    const typeOptions = [
      {
        value: FillType.Solid,
        label: '无'
      },
      {
        value: FillType.Linear,
        label: '线性渐变'
      },
      {
        value: FillType.Radial,
        label: '径向渐变'
      }
    ];

    //#region 圆角
    /** 圆角 */
    const radius = ref<number | string>(computedOptions.value.radius[0]);

    const radiusChange = () => {
      if (typeof radius.value === 'number') {
        computedOptions.value.radius = [radius.value, radius.value, radius.value, radius.value];
      }
    };

    const flag = computed(
      () => !computedOptions.value.radius.every((item: number) => item === computedOptions.value.radius[0])
    );

    const showRadius = ref(flag.value ? true : false);

    const { active } = useDesignStore();
    const activeBlock = computed(() => active.value.block as WidgetBlock);

    watch(
      () => activeBlock.value.id,
      () => {
        radius.value = computedOptions.value.radius[0];
        showRadius.value = flag.value;
      },
      {
        immediate: true
      }
    );

    watch(
      () => flag.value,
      () => {
        radius.value = flag.value ? '多值' : computedOptions.value.radius[0];
      },
      {
        immediate: true
      }
    );

    const callbackPlace = (val: string) => {
      if (isString(val) && isNaN(Number(val))) {
        const isEqual = new Set(computedOptions.value.radius).size === 1;
        return isEqual ? computedOptions.value.radius[0] : '多值';
      } else {
        return true;
      }
    };

    //#endregion

    return {
      computedOptions,
      typeOptions,
      showTypeOptions,
      radius,
      radiusChange,
      showRadius,
      callbackPlace,
      activeBlock
    };
  }
});
