<template>
  <div class="vis-config-tab-group-option">
    <!-- 选项 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">选项</div>
      <div class="vis-config-card__body">
        <!-- 模式 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">模式</div>
              <div class="vis-form-field__content">
                <vis-button-group v-model="tabOptions.selectMode" :options="selectOptions" />
              </div>
            </div>
            <div class="w-[120px]">
              <div class="vis-form-field__label row items-center">
                <div class="q-mr-xs">默认选中项</div>
                <q-icon name="help_ouline" size="12px" class="cursor-pointer !display-block">
                  <q-tooltip :offset="[10, 10]" max-width="120px" class="bg-white !text-gray-600"
                    >默认状态下的选中项。从1开始，0即不选中,多选模式下以逗号分隔默认选中的选项（例如: 1,2,3）</q-tooltip
                  >
                </q-icon>
              </div>
              <div class="vis-form-field__content">
                <q-input
                  v-model="tabOptions.defaultIndex"
                  class="vis-number rounded-borders flex-1 px-2 pr-0 vis-field--mini"
                  type="text"
                  borderless
                  dense
                />
              </div>
            </div>
          </div>
        </div>
        <!-- 文本 -->
        <vis-text
          textTitle="文本"
          :option="tabOptions.style.fontStyle"
          mode="base"
          :mini="true"
          :textEffects="tabOptions.style.textEffects"
        >
        </vis-text>

        <!-- 文本溢出 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">文本溢出</div>
              <div class="vis-form-field__content">
                <vis-select v-model="tabOptions.style.overflow" :options="overflowOptions" :popHeight="240">
                </vis-select>
              </div>
            </div>

            <div class="vis-form-field" :class="tabOptions.style.overflow === 3 ? 'visible' : 'invisible'">
              <div class="vis-form-field__label">滚动速度</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.style.scrollSpeed" :min="1" />
              </div>
            </div>
          </div>
        </div>

        <!-- 背景 -->
        <vis-tab-surface
          v-model:fillPaints="tabStyle.fillPaints"
          v-model:stroke="tabStyle.stroke"
          v-model:effects="tabStyle.effects"
        ></vis-tab-surface>

        <!-- 图标配置 -->
        <vis-tab-icon-selector :icon-option="tabOptions.adorn" :visible="tabStyle.fontStyle.visible" />

        <div
          class="vis-form-inline"
          v-if="tabOptions.adorn.type === AdornType.Image"
          :class="{ 'vis-invisible': !tabOptions.adorn.visible }"
        >
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">宽度</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="tabOptions.adorn.image.width"
                  precision="0"
                  :min="10"
                  icon="hticon-vis-letter-w"
                  @change="(value:number,oldValue:number)=>changeSize(value,oldValue,'width')"
                />
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__label">高度</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="tabOptions.adorn.image.height"
                  precision="0"
                  :min="10"
                  icon="hticon-vis-letter-h"
                  @change="(value:number,oldValue:number)=>changeSize(value,oldValue,'height')"
                />
              </div>
            </div>
          </div>
          <q-btn class="btn-field" :class="{ active: tabOptions.adorn.image.aspectRatio }" @click="onAspectRatio">
            <ht-icon class="vis-icon" name="vis-associate" />
            <q-tooltip> 锁定纵横比 </q-tooltip>
          </q-btn>
        </div>

        <div class="vis-form-inline" v-else :class="{ 'vis-invisible': !tabOptions.adorn.visible }">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">装饰颜色</div>
              <div class="vis-form-field__content">
                <!-- <vis-number v-model="tabOptions.icon.width" precision="0" :min="0" icon="hticon-vis-letter-w" /> -->
                <vis-fill
                  v-model="tabOptions.adorn.icon.fillPaints"
                  :minusWidth="0"
                  tooltip="颜色"
                  :onlyColor="true"
                  :showEyes="false"
                />
              </div>
            </div>

            <div class="vis-form-field--width-42">
              <div class="vis-form-field__label">大小</div>
              <div class="vis-form-field__content">
                <vis-font-size v-model="tabOptions.adorn.icon.size" noIcon />
              </div>
            </div>
          </div>
        </div>

        <!-- 选中项自动定位 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <q-checkbox v-model="tabOptions.autoPosition" label="自动定位到选中项" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <q-separator />
    </div>

    <!-- 布局 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">布局</div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label"></div>
              <div class="vis-form-field__content">
                <vis-button-group v-model="tabOptions.layout.direction" :options="directionOptions" />
              </div>
            </div>
          </div>
          <q-btn
            v-if="
              tabOptions.layout.direction === DisplayDirection.Horizontal &&
              tabOptions.layout.resizeX === ResizeType.Fixed
            "
            class="btn-field"
            :class="{ active: tabOptions.layout.flowWrap }"
            @click="tabOptions.layout.flowWrap = !tabOptions.layout.flowWrap"
          >
            <ht-icon name="vis-layout-warp" />
            <q-tooltip> 换行 </q-tooltip>
          </q-btn>
        </div>

        <!-- 列宽 行高 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">宽度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="tabOptions.layout.width"
                  :min="10"
                  :icon="`hticon-vis-${tabOptions.layout.resizeX === ResizeType.Adapt ? 'resize-adapt-x' : 'letter-w'}`"
                  class="pr-0"
                  :input-class="tabOptions.layout.resizeX !== ResizeType.Fixed ? 'hidden' : ''"
                >
                  <template v-slot:default v-if="tabOptions.layout.resizeX !== ResizeType.Fixed">
                    <span class="text-font-regular"> 适应 </span>
                  </template>
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu v-model="showMenuWidth" style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item
                            :active="tabOptions.layout.resizeX === ResizeType.Adapt"
                            @click="onChangeResize('resizeX', ResizeType.Adapt)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-adapt-x" />
                            </q-item-section>
                            <q-item-section>适应容器</q-item-section>
                          </q-item>
                          <q-item
                            :active="tabOptions.layout.resizeX === ResizeType.Fixed"
                            @click="onChangeResize('resizeX', ResizeType.Fixed)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-fixed-x" />
                            </q-item-section>
                            <q-item-section>固定尺寸</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__label">高度</div>
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="tabOptions.layout.height"
                  :min="10"
                  :icon="`hticon-vis-${tabOptions.layout.resizeY === ResizeType.Adapt ? 'resize-adapt-y' : 'letter-h'}`"
                  class="pr-0"
                  :input-class="tabOptions.layout.resizeY !== ResizeType.Fixed ? 'hidden' : ''"
                >
                  <template v-slot:default v-if="tabOptions.layout.resizeY !== ResizeType.Fixed">
                    <span class="text-font-regular"> 适应 </span>
                  </template>
                  <template v-slot:append>
                    <q-btn>
                      <q-icon name="keyboard_arrow_down" class="!text-xs" />
                      <q-menu v-model="showMenuHeight" style="width: 150px" class="vis-menu" dense>
                        <q-list dense>
                          <q-item
                            :active="tabOptions.layout.resizeY === ResizeType.Adapt"
                            @click="onChangeResize('resizeY', ResizeType.Adapt)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-adapt-y" />
                            </q-item-section>
                            <q-item-section>适应容器</q-item-section>
                          </q-item>
                          <q-item
                            :active="tabOptions.layout.resizeY === ResizeType.Fixed"
                            @click="onChangeResize('resizeY', ResizeType.Fixed)"
                            clickable
                            v-close-popup
                          >
                            <q-item-section avatar>
                              <ht-icon name="vis-resize-fixed-y" />
                            </q-item-section>
                            <q-item-section>固定尺寸</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </template>
                </vis-mix-input>
              </div>
            </div>
          </div>
        </div>
        <!-- 网格布局 -->
        <div class="vis-form-inline" v-if="tabOptions.layout.direction === DisplayDirection.Grid">
          <div class="vis-form-inline__content--minus-32 !items-start">
            <div class="vis-form-field">
              <div class="vis-form-field__label">网格</div>
              <div class="vis-form-field__content">
                <div
                  v-if="tabOptions.layout.direction === DisplayDirection.Grid"
                  class="vis-layout-grid flex flex-center text-xs"
                  @click="onShowPopup"
                >
                  {{ tabOptions.layout.row }} × {{ tabOptions.layout.column }}
                </div>
                <vis-popup title="网格" ref="popupRef" :target="false" @hide="popupShow = false" width="260px">
                  <div class="vis-form-inline !flex-nowrap">
                    <div class="vis-form-inline__content--minus-32 !items-center">
                      <div class="vis-form-field !flex-1">
                        <div class="vis-form-field__content">
                          <vis-number
                            v-model="tabOptions.layout.row"
                            precision="0"
                            :min="1"
                            icon="hticon-vis-grid-row"
                          />
                        </div>
                      </div>
                      ×
                      <div class="vis-form-field !flex-1">
                        <div class="vis-form-field__content">
                          <vis-number
                            v-model="tabOptions.layout.column"
                            precision="0"
                            :min="1"
                            icon="hticon-vis-grid-col"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="vis-menu-grid_picker row gap-1 pt-3"
                    @mousemove="onMouseMovePicker"
                    @mouseleave="onMouseLeavePicker"
                  >
                    <div class="row gap-1" v-for="row in 12" :key="row">
                      <div
                        :class="{
                          hand: true,
                          hover: row <= hoverGrid[0] && col <= hoverGrid[1],
                          active: row <= tabOptions.layout.row && col <= tabOptions.layout.column
                        }"
                        :data-row="row"
                        :data-col="col"
                        v-for="col in 12"
                        :key="col"
                        @click="onClickGrid(row, col)"
                      >
                        <q-tooltip> {{ row }} × {{ col }} </q-tooltip>
                      </div>
                    </div>
                  </div>
                </vis-popup>
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__label">间距</div>
              <div class="vis-form-field__content column">
                <template v-if="tabOptions.layout.direction === DisplayDirection.Grid">
                  <vis-number
                    v-model="tabOptions.layout.horizontalGap"
                    :min="0"
                    precision="0"
                    icon="hticon-vis-gap-horizontal"
                  />
                  <vis-number
                    v-model="tabOptions.layout.verticalGap"
                    :min="0"
                    precision="0"
                    icon="hticon-vis-gap-vertical"
                  />
                </template>
              </div>
            </div>
          </div>
        </div>
        <!-- 间距 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field" v-if="tabOptions.layout.direction === DisplayDirection.Horizontal">
              <div class="vis-form-field__label">列距</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="tabOptions.layout.horizontalGap"
                  :min="0"
                  precision="0"
                  icon="hticon-vis-padding-x"
                />
              </div>
            </div>

            <div class="vis-form-field" v-if="getShowLineGutter">
              <div class="vis-form-field__label">行距</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="tabOptions.layout.verticalGap"
                  :min="0"
                  precision="0"
                  icon="hticon-vis-padding-y"
                />
              </div>
            </div>
            <div class="vis-form-field" v-if="isAllRadius">
              <div class="vis-form-field__label">圆角</div>
              <div class="vis-form-field__content">
                <!-- <vis-mix-input
                  v-model="allRadius"
                  icon="hticon-vis-radius"
                  precision="0"
                  @update:model-value="(val:number) => radiusChange(val, 'all')"
                  :min="0"
                /> -->
                <vis-number
                  v-model="allRadius"
                  icon="hticon-vis-radius"
                  precision="0"
                  :min="0"
                  place-str="多值"
                  :callback-place="(val:string) => callbackPlace(val, 'all')"
                  @update:model-value="(val:number) => radiusChange(val, 'all')"
                />
              </div>
            </div>
          </div>

          <q-btn
            v-if="isAllRadius"
            flat
            class="btn-field"
            :class="{ active: showRadius }"
            @click="showRadius = !showRadius"
          >
            <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
            <q-tooltip> 单独设置 </q-tooltip>
          </q-btn>
        </div>
        <template v-if="showRadius && isAllRadius">
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label"></div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[0]" precision="0" icon="hticon-vis-radius-lt" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[1]" precision="0" icon="hticon-vis-radius-rt" :min="0" />
                </div>
              </div>
            </div>
          </div>
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[3]" precision="0" icon="hticon-vis-radius-lb" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[2]" precision="0" icon="hticon-vis-radius-rb" :min="0" />
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- 圆角 -->
        <div class="vis-form-inline" v-if="!isAllRadius && !showRadius">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">圆角</div>
              <div class="vis-form-field__content">
                <vis-number
                  v-model="leftRadius"
                  icon="hticon-vis-radius-left"
                  precision="0"
                  @update:model-value="(val:number) => radiusChange(val, 'left')"
                  :min="0"
                  place-str="多值"
                  :callback-place="(val:string) => callbackPlace(val, 'left')"
                />
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <vis-mix-input
                  v-model="rightRadius"
                  precision="0"
                  icon="hticon-vis-radius-right"
                  @update:model-value="(val:number) => radiusChange(val, 'right')"
                  :min="0"
                  place-str="多值"
                  :callback-place="(val:string) => callbackPlace(val, 'right')"
                />
              </div>
            </div>
          </div>
          <q-btn flat class="btn-field" :class="{ active: showRadius }" @click="showRadius = !showRadius">
            <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
            <q-tooltip> 单独设置 </q-tooltip>
          </q-btn>
        </div>

        <template v-if="showRadius && !isAllRadius">
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__label">圆角</div>
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[0]" precision="0" icon="hticon-vis-radius-lt" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[1]" precision="0" icon="hticon-vis-radius-rt" :min="0" />
                </div>
              </div>
            </div>
            <q-btn flat class="btn-field" :class="{ active: showRadius }" @click="showRadius = !showRadius">
              <ht-icon class="vis-icon" name="hticon-vis-radius-s" />
              <q-tooltip> 单独设置 </q-tooltip>
            </q-btn>
          </div>
          <div class="vis-form-inline">
            <div class="vis-form-inline__content--minus-32">
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[3]" precision="0" icon="hticon-vis-radius-lb" :min="0" />
                </div>
              </div>
              <div class="vis-form-field">
                <div class="vis-form-field__content">
                  <vis-number v-model="tabOptions.style.radius[2]" precision="0" icon="hticon-vis-radius-rb" :min="0" />
                </div>
              </div>
            </div>
          </div>
        </template>

        <!-- 滚动条 -->
        <div class="vis-form-inline">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__content">
                <q-checkbox v-model="tabOptions.layout.scrollbar" label="滚动条" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <q-separator />
    </div>

    <!-- 选中状态 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>选中状态</span>
        <q-btn flat dense>
          <ht-icon class="vis-icon" name="vis-add" />
          <q-menu class="vis-menu" style="width: 120px">
            <q-list dense style="font-size: 10px">
              <q-item clickable :active="!!tabStyle.active.fontStyle" @click="toggleStatus('active', 'text')">
                <q-item-section class="text-left">文本</q-item-section>
              </q-item>
              <q-item clickable :active="!!tabStyle.active.adorn" @click="toggleStatus('active', 'adorn')">
                <q-item-section class="text-left">装饰</q-item-section>
              </q-item>
              <q-item clickable :active="!!tabStyle.active.fillPaints" @click="toggleStatus('active', 'graph')">
                <q-item-section class="text-left">外观</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
      <div class="vis-config-card__body" v-if="tabStyle.active">
        <vis-config-tab-status v-model="tabStyle.active" title="选中" />
      </div>
      <q-separator />
    </div>

    <!-- 悬浮状态 -->
    <div class="vis-config-card">
      <div class="vis-config-card__header">
        <span>悬浮状态</span>
        <q-btn flat dense>
          <ht-icon class="vis-icon" name="vis-add" />
          <q-menu class="vis-menu" style="width: 120px">
            <q-list dense style="font-size: 10px">
              <q-item clickable :active="!!tabStyle.hover.fontStyle" @click="toggleStatus('hover', 'text')">
                <q-item-section class="text-left">文本</q-item-section>
              </q-item>
              <q-item clickable :active="!!tabStyle.hover.adorn" @click="toggleStatus('hover', 'adorn')">
                <q-item-section class="text-left">装饰</q-item-section>
              </q-item>
              <q-item clickable :active="!!tabStyle.hover.fillPaints" @click="toggleStatus('hover', 'graph')">
                <q-item-section class="text-left">外观</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
      <div class="vis-config-card__body" v-if="tabStyle.hover">
        <vis-config-tab-status v-model="tabStyle.hover" title="悬浮" />
      </div>
      <q-separator />
    </div>

    <!-- 自动轮播 -->
    <div class="vis-config-card" v-if="tabOptions.selectMode === SelectMode.Single">
      <div class="vis-config-card__header">
        <span>轮播</span>
        <q-btn flat dense @click="toggle">
          <ht-icon class="vis-icon" :name="`vis-${tabOptions.carousel ? 'remove' : 'add'}`" />
        </q-btn>
      </div>
      <div class="vis-config-card__body">
        <div class="vis-form-inline" v-if="tabOptions.carousel">
          <div class="vis-form-inline__content--minus-32">
            <div class="vis-form-field">
              <div class="vis-form-field__label">间隔时长</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.carousel.interval">
                  <template #append>
                    <span>s</span>
                  </template>
                </vis-number>
              </div>
            </div>

            <div class="vis-form-field">
              <div class="vis-form-field__label">点击停留</div>
              <div class="vis-form-field__content">
                <vis-number v-model="tabOptions.carousel.clickTime">
                  <template #append>
                    <span>s</span>
                  </template>
                </vis-number>
              </div>
            </div>
          </div>
        </div>
      </div>
      <q-separator />
    </div>
  </div>
</template>

<script lang="ts" src="./tab-group.ts"></script>
