import { ChartType, type Color, type FillPaints, type SeniorFont } from '../../../models';
import { FillType } from '../../../models';

/**
 * 图表工具函数
 * <AUTHOR>
 */

/**
 * 图形类型映射
 */
export const markTypeMap: Record<string, string | string[]> = {
  [ChartType.Bar]: 'interval',
  [ChartType.StackBar]: 'interval',
  [ChartType.PercentageBar]: 'interval',
  [ChartType.BulletBar]: 'interval',
  [ChartType.Strip]: 'interval',
  [ChartType.StackStrip]: 'interval',
  [ChartType.PercentageStrip]: 'interval',
  [ChartType.Line]: 'line'
};

/**
 * 位置映射 - 支持8个方向的位置配置
 */
export const positionMap: Record<string, string> = {
  'top-center': 'top',
  'top-left': 'top',
  'top-right': 'top',
  'bottom-center': 'bottom',
  'bottom-left': 'bottom',
  'bottom-right': 'bottom',
  'left-center': 'left',
  'left-top': 'left',
  'left-bottom': 'left',
  'center-left': 'left',
  'right-center': 'right',
  'right-top': 'right',
  'right-bottom': 'right',
  'center-right': 'right'
};

/**
 * 布局对齐映射 - 用于layout.justifyContent配置
 */
export const layoutJustifyMap: Record<string, string> = {
  'top-center': 'center',
  'top-left': 'flex-start',
  'top-right': 'flex-end',
  'bottom-center': 'center',
  'bottom-left': 'flex-start',
  'bottom-right': 'flex-end',
  'left-center': 'center',
  'left-top': 'flex-start',
  'left-bottom': 'flex-end',
  'center-left': 'center',
  'right-center': 'center',
  'right-top': 'flex-start',
  'right-bottom': 'flex-end',
  'center-right': 'center'
};

/**
 * 方向映射
 */
export const directionMap: Record<string, string> = {
  horizontal: 'horizontal',
  vertical: 'vertical'
};

/**
 * 颜色RGBA转换为CSS颜色字符串
 */
export const rgbaToColor = (color: Color): string => {
  if (!color) return '';
  const { r, g, b, a } = color;
  return `rgba(${r}, ${g}, ${b}, ${a})`;
};

/**
 * 将 FillPaints 转换为 G2 支持的颜色格式
 * 支持纯色、线性渐变、环形渐变等
 */
export const fillPaintsToG2Color = (fillPaints: FillPaints): string | undefined => {
  if (!fillPaints?.visible) return undefined;

  const { type, color, stops, rotation = 0, opacity = 1 } = fillPaints;

  // 纯色
  if (type === FillType.Solid) {
    return rgbaToColor(color as Color);
  }

  // 渐变色
  if (stops && stops.length > 0) {
    const sortedStops = [...stops].sort((a, b) => a.position - b.position);
    const colorStops = sortedStops
      .map((stop) => {
        const { r, g, b, a } = stop.color;
        return `${stop.position}:rgba(${r},${g},${b},${a * opacity})`;
      })
      .join(' ');

    switch (type) {
      case FillType.Radial:
        // 环形渐变: r(中心x, 中心y, 半径) 0:颜色 1:颜色
        return `r(0.5, 0.5, 0.5) ${colorStops}`;
      case FillType.Linear:
      case FillType.Angular:
      case FillType.Diamond:
      default:
        // 默认使用线性渐变: l(角度) 0:颜色 0.5:颜色 1:颜色
        return `l(${rotation}) ${colorStops}`;
    }
  }

  // 图片填充暂不支持，返回纯色
  return rgbaToColor(color as Color);
};

/**
 * 获取颜色值
 */
export const getColorValue = (paintConfig: FillPaints | undefined): string | undefined => {
  return paintConfig ? fillPaintsToG2Color(paintConfig) : undefined;
};

/**
 * 根据描边类型生成对应的虚线配置
 * @param stroke 描边配置对象
 * @returns G2 支持的虚线配置数组或字符串
 */
export const getLineDashFromStroke = (stroke: any): [number, number] | string | undefined => {
  if (!stroke) return undefined;

  // 根据描边类型生成对应的虚线配置
  switch (stroke.style) {
    case 'dashed':
      // 虚线：[虚线长度, 间隔长度]
      return [5, 5];
    case 'dotted':
      // 点线：[点长度, 间隔长度]
      return [1, 3];
    case 'none':
      return [0, 1];
    case 'solid':
    default:
      return '-';
  }
};

/**
 * 映射字体样式配置
 */
export const mapFontStyle = (fontStyle: SeniorFont): Record<string, any> => {
  if (!fontStyle) return {};

  return {
    fontSize: fontStyle.fontSize || 12,
    fontFamily: fontStyle.fontFamily || '默认字体',
    fontWeight: fontStyle.fontWeight || 400,
    fill: fillPaintsToG2Color(fontStyle.fillPaints)
  };
};

/**
 * 验证配置对象
 */
export const validateConfig = (config: any): config is any => {
  if (!config || typeof config !== 'object') {
    return false;
  }
  return true;
};

/**
 * 获取嵌套对象的值
 */
export function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * 设置嵌套对象的值
 */
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  const lastKey = keys.pop()!;
  const target = keys.reduce((current, key) => {
    if (!current[key]) current[key] = {};
    return current[key];
  }, obj);
  target[lastKey] = value;
}

/**
 * 处理单个映射项
 */
export function processMappingItem(sourceValue: any, mapping: any, sourceConfig?: any, ...extraArgs: any[]): any {
  if (mapping.transform) {
    // 如果 transform 函数需要完整配置对象，传递 sourceConfig 和额外参数
    return mapping.transform(sourceValue, sourceConfig, ...extraArgs);
  }

  if (sourceValue !== undefined && sourceValue !== null) {
    return sourceValue;
  }

  return mapping.default;
}

/**
 * 通用映射配置函数
 */
export function mapConfig(sourceConfig: any, targetConfig: any, mappingConfig: any, ...extraArgs: any[]): void {
  Object.entries(mappingConfig).forEach(([targetKey, mapping]: [string, any]) => {
    // 检查条件
    if (mapping.condition && !mapping.condition(sourceConfig)) {
      // 如果有 fallback，使用 fallback
      if (mapping.fallback !== undefined) {
        setNestedValue(targetConfig, targetKey, mapping.fallback);
      }
      return;
    }

    // 获取源值
    const sourceValue = getNestedValue(sourceConfig, mapping.source);

    // 处理映射
    const mappedValue = processMappingItem(sourceValue, mapping, sourceConfig, ...extraArgs);

    if (mappedValue !== undefined) {
      setNestedValue(targetConfig, targetKey, mappedValue);
    }
  });
}
