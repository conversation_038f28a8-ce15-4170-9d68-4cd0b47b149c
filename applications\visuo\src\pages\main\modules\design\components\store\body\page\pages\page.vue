<template>
  <div class="vis-store-page-container h-80" :style="{ height: `${pagesHeight}px` }">
    <!-- 头部 -->
    <div class="vis-store-page-container-header" v-show="!isSearchHide">
      <span class="vis-store-page-container-title">页面</span>
      <div class="vis-store-page-container-btn-group">
        <q-btn class="vis-store-page-container-btn" flat @click="onUpdateSearchHide">
          <ht-icon class="vis-icon" name="hticon-vis-search"></ht-icon>
          <q-tooltip> 搜索 </q-tooltip>
        </q-btn>
        <q-btn flat class="vis-store-page-container-btn" @click="onAdd('group')" :disabled="isDisabledGroup">
          <ht-icon class="vis-icon" name="hticon-vis-add-folder"></ht-icon>
          <q-tooltip> 添加页面分类 </q-tooltip>
        </q-btn>
        <q-btn flat class="vis-store-page-container-btn" @click="onAdd('page')">
          <ht-icon class="vis-icon" name="hticon-vis-add-page"></ht-icon>
          <q-tooltip> 添加页面 </q-tooltip>
        </q-btn>
        <q-btn class="vis-store-page-container-btn" flat @click="onUpdateExpandAllHide">
          <ht-icon
            class="vis-icon"
            :name="isExpandHide ? 'hticon-vis-collapse-all' : 'hticon-vis-expand-all'"
          ></ht-icon>
          <q-tooltip> {{ isExpandHide ? '收起全部' : '展开全部' }} </q-tooltip>
        </q-btn>
      </div>
    </div>
    <!-- 筛选框 -->
    <div class="vis-store-page-container-header" v-show="isSearchHide">
      <div class="vis-store-page-container-header-search">
        <!-- clearable @clear="filterRef?.focus()"-->
        <q-input
          class="vis-store-page-container-header-input"
          ref="filterRef"
          rounded
          dense
          borderless
          placeholder="搜索页面名称"
          v-model="keyWord"
          @update:model-value="onKeyWordChange"
          @blur="onUpdateSearchHide"
        >
          <template #prepend>
            <ht-icon class="text-16px" name="hticon-vis-search"></ht-icon>
          </template>
        </q-input>
        <div class="vis-store-page-container-btn-group">
          <q-btn class="vis-store-page-container-btn" flat @click="onCloseSearch">
            <ht-icon class="vis-icon" name="hticon-vis-close"></ht-icon>
          </q-btn>
        </div>
      </div>
    </div>
    <!-- 拖拽树 -->
    <q-scroll-area ref="scrollAreaRef" class="h-[calc(100%-40px)] px-3">
      <div
        :class="['vis-store-page-container-tree_layout', { 'is-dragging': isDragging }]"
        v-click-outside="onClickOutside"
      >
        <div v-if="!pages.length && isSearchHide && keyWord" class="empty-content">没有找到匹配的结果</div>
        <vis-page-item
          v-else
          :nodes="pages"
          @drag-end="onDragEnd"
          @drag-add="onDragAdd"
          @drag-start="onDragStart"
          @drag-move="onCheckMove"
          :drag-target-parent-id="dragTargetParentId"
          :selected-ids="selectedIds"
          @update:selected-ids="onUpdateSelected"
          :depth="depth"
          :expanded-map="expandedMap"
          @update:expanded-map="onUpdateExpandHide"
          :is-editing-status="isEditingStatus"
          :is-search-hide="isSearchHide"
          @context-menu="onOpenContextMenu"
        />
      </div>
    </q-scroll-area>
  </div>
</template>

<script lang="ts" src="./page.ts"></script>
<style lang="scss" scoped src="./page.scss"></style>
