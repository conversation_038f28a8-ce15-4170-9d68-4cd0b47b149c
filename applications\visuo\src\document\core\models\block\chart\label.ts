import { isBoolean, isNumber } from 'lodash-es';
import { SeniorFont, Stroke, Effects, RelativePosition, ShowType, FillPaints } from '../../ui';

/**
 * 图表标签类
 * <AUTHOR>
 */

/**
 * 图标标签基础类
 */
export class BaseChartLabel {
  /** 标签是否可见 */
  visible: boolean = true;

  /** 标签水平偏移量 */
  offsetX: number = 0;

  /** 标签垂直偏移量 */
  offsetY: number = 0;

  /** 标签文字样式配置 */
  fontStyle: SeniorFont = new SeniorFont();

  /** 标签文字描边样式 */
  stroke: Stroke = new Stroke();

  /** 标签阴影和特效 */
  effects: Effects = new Effects();

  constructor(visible?: boolean, offsetX?: number, offsetY?: number) {
    isBoolean(visible) && (this.visible = visible);
    isNumber(offsetX) && (this.offsetX = offsetX);
    isNumber(offsetY) && (this.offsetY = offsetY);
  }
}

/**
 * 图标文字类
 */
export class ChartLabel extends BaseChartLabel {
  /** 标签透明度，取值范围 0-1 */
  opacity: number = 1;

  /** 标签与对应刻度的间距 */
  gap: number = 8;

  /** 标签旋转角度，单位为度 */
  angle: number = 0;
}

/**
 * 数据标签类
 */
export class TotalLabel extends BaseChartLabel {
  /** 标签位置 */
  position: RelativePosition = RelativePosition.Top;

  /** 显示类型 */
  showType: ShowType = ShowType.All;

  /** 自动隐藏重叠标签 */
  autoHide: boolean = true;

  /** 标记最大值 */
  showMax = false;
  /** 标记最小值 */
  showMin = false;

  /** 标记第一个值 */
  showFirst = false;

  /** 标记最后一个值 */
  showLast = false;

  /** 背景 */
  fillPaints: FillPaints = new FillPaints();

  /** 圆角 */
  radius = [0, 0, 0, 0];

  constructor(visible?: boolean) {
    super(visible);
  }
}
