import { defineComponent, ref, computed, watch, nextTick, onMounted } from 'vue';
import { VisDesignRuler, VisDesignInfiniteViewer, VisDesignCanvas, VisDesignSelecto } from './content';
import { VisDesignConfig } from './config';
import { VisDesignStore } from './store';
import { useDesignStore, useActionStore } from '../stores';
import { DocumentService } from '@vis/document-core';
import { registerDesignLibraries } from './libraries';
import draggable from 'vuedraggable';
import {
  Block,
  Document,
  Frame,
  GraphType,
  Page,
  WidgetConfig,
  setupGraph,
  useDocumentStore,
  useFrame
} from '@vis/document-core';
import { useGraph, usePage, useWidget, useAction } from '../hooks';
import { setupUi } from './ui';
import VisDesignDock from './dock/dock.vue';
import VisDesignShortcuts from './shortcuts/shortcuts.vue';
import type { ContextMenuType } from '../models';

export default defineComponent({
  name: 'vis-design',
  components: {
    draggable,
    VisDesignRuler,
    VisDesignInfiniteViewer,
    VisDesignCanvas,
    VisDesignSelecto,
    VisDesignConfig,
    VisDesignStore,
    VisDesignDock,
    VisDesignShortcuts
  },
  props: {
    id: String
  },
  setup(props) {
    registerDesignLibraries();
    setupGraph();
    setupUi();
    // const $q = useQuasar();
    // $q.dark.set(true);

    const designStore = useDesignStore();
    const docStore = useDocumentStore();
    const { actions, contextMenus } = useActionStore();

    const { switchPage } = usePage();

    onMounted(() => {
      initData();
      DocumentService.setFontsStyle();
      DocumentService.loadIconConfig();
      DocumentService.loadEffectsConfig();
    });

    /** 加载文件数据 */
    const initData = async () => {
      if (props.id) {
        DocumentService.info(props.id).then(async (data) => {
          let doc = new Document();
          if (data && Object.keys(data).length) {
            doc = data;
          } else {
            doc.home = doc.children[0].id;
          }
          docStore.document.value = doc;
          switchPage(doc.home);
        });
      }
    };

    //#region 从左侧面板拖拽添加组件

    const rulerState = designStore.rulerState;
    const horizontalGuidesRef = designStore.horizontalGuidesRef;
    const verticalGuidesRef = designStore.verticalGuidesRef;
    const infiniteCanvasRef = designStore.infiniteCanvasRef;
    const canvasState = computed(() => designStore.canvasState.value);

    const dropbox = computed(() => designStore.canvasState.value.dropbox);

    const { handleWidget } = useWidget();
    const { getCanvasPosSize, addBlockToPage, addFrameBySize, activeGraphs } = useGraph();
    const libraryList = ref<WidgetConfig[]>([]);

    const page = computed(() => {
      return designStore.active.value.page as Page;
    });

    const addWidgetBlock = (param: { clone: HTMLElement; newIndex: number; originalEvent: DragEvent }) => {
      if (param.clone) {
        const config = libraryList.value[param.newIndex];
        const event = param.originalEvent;

        let { x, y } = getCanvasPosSize(event.clientX, event.clientY);
        // x,y减去组件的宽度/2，是为了让组件中心点在鼠标位置
        x = x - 100 / 2;
        y = y - 100 / 2;
        const { widgetBlock, block: blockConfig } = handleWidget(config);
        const block = Object.assign(new Block(), blockConfig);
        block.width = config.width;
        block.height = config.height;
        block.transform.translate = [x, y];
        block.decoration = widgetBlock.id;
        docStore.document.value.blocks.push(widgetBlock);

        addBlockToPage(block);
        canvasState.value.gridRowCol = undefined;
      }
    };

    //#endregion

    const { prepareSize } = useFrame();

    // #region 操作面板显示/隐藏
    const isPanelShow = ref(actions.value.operationPanel.active);
    const onUpdatePanelShow = (value: boolean) => {
      actions.value.operationPanel.active = value;
      actions.value.operationPanel.disable = !value;
    };
    watch(
      () => actions.value.operationPanel.active,
      () => {
        isPanelShow.value = actions.value.operationPanel.active;
      }
    );
    // #endregion

    // #region 左右两侧拖拽宽度计算
    const leftWidth = computed(() => designStore.panelState.value.leftWidth);
    const rightWidth = computed(() => designStore.panelState.value.rightWidth);
    const drag = ref<'left' | 'right'>('left');
    const isDragging = ref(false);
    const startX = ref(0);
    const startWidth = ref(0);
    // 开始拖拽
    const onDrag = (key: 'left' | 'right', event: MouseEvent) => {
      isDragging.value = true;
      drag.value = key;
      startX.value = event.clientX;
      startWidth.value = drag.value === 'left' ? leftWidth.value : rightWidth.value;
      document.addEventListener('mousemove', onDragMove);
      document.addEventListener('mouseup', onDragEnd);
      event.preventDefault();
    };
    // 拖拽中
    const onDragMove = (event: MouseEvent) => {
      if (!isDragging.value) return;
      const deltaX = drag.value === 'left' ? event.clientX - startX.value : startX.value - event.clientX;
      let newWidth = startWidth.value + deltaX;
      newWidth = Math.max(241, Math.min(500, newWidth));
      if (drag.value === 'left') {
        designStore.panelState.value.leftWidth = newWidth;
      } else {
        designStore.panelState.value.rightWidth = newWidth;
      }
    };
    // 拖拽结束
    const onDragEnd = () => {
      isDragging.value = false;
      document.removeEventListener('mousemove', onDragMove);
      document.removeEventListener('mouseup', onDragEnd);
    };
    //#endregion

    // #region 右键菜单
    const contextMenuRef = designStore.contextMenuRef;
    const onOpenContextMenu = (event: MouseEvent) => {
      const canvas = (event.target as HTMLElement).closest('.vis-design-infinite-canvas'); // 画布
      const moveableArea = (event.target as HTMLElement).closest('.moveable-area'); // 选区
      const targetGraph = (event.target as HTMLElement).closest('.vis-graph'); // 图形

      if (canvas && !moveableArea && !targetGraph) {
        activeGraphs();
      }

      if (targetGraph) {
        activeGraphs([targetGraph.id]);
      }

      let menus: ContextMenuType = [];
      if (designStore.active.value.graphs.length >= 1) {
        menus = contextMenus.graph;
        const node = designStore.active.value.graphs[0];
        // 设置为主容器条件：1 单选操作；2 根容器（parent为""）；3 容器（Frame）类型；
        actions.value.setAsMainFrame.disable =
          designStore.active.value.graphs.length > 1 || !(!node.parent && node.type === GraphType.Frame);
        // 设置封面条件：1 单选操作；2 根容器（parent为""）；3 容器（Frame）类型
        actions.value.setAsCover.disable =
          designStore.active.value.graphs.length > 1 || !(node.type === GraphType.Frame);
        // 取消容器条件：1 容器类型；
        actions.value.cancelFrame.disable = !(node.type === GraphType.Frame);
      } else {
        menus = contextMenus.canvas;
      }

      // 剪贴板clipboard无内容时，需禁用粘贴类操作：粘贴/粘贴到鼠标位置/粘贴为（粘贴图形样式/粘贴图形配置/粘贴并替换）
      actions.value.pasteGraph.disable = !designStore.canvasState.value.clipboard.length;
      actions.value.pasteToMousePosition.disable = !designStore.canvasState.value.clipboard.length;
      actions.value.pasteGraphStyle.disable = !designStore.canvasState.value.clipboard.length;
      actions.value.pasteGraphConfig.disable = !designStore.canvasState.value.clipboard.length;
      actions.value.pasteAndReplaceGraph.disable = !designStore.canvasState.value.clipboard.length;

      contextMenuRef.value?.open({
        menus,
        x: event.clientX,
        y: event.clientY,
        action: (actionKey: string) => {
          (useAction() as any)[actionKey] && (useAction() as any)[actionKey]();
        }
      });
    };
    // #endregion

    // #region 标尺显示/隐藏
    const isRulerShow = computed(() => actions.value.ruler.active);
    const horizontalPos = computed(() => rulerState.value.horizontalPos);
    const verticalPos = computed(() => rulerState.value.verticalPos);
    const canvasWarpStyle = computed(() => {
      return {
        top: `${horizontalPos.value}px`,
        left: `${verticalPos.value}px`,
        width: `calc(100% - ${verticalPos.value}px)`,
        height: `calc(100% - ${horizontalPos.value}px)`
      };
    });
    // #endregion

    return {
      // 标尺
      rulerState,

      // 画布添加组件
      dropbox,
      libraryList,
      page,
      addWidgetBlock,

      prepareSize,
      addFrameBySize,

      isPanelShow,
      onUpdatePanelShow,

      leftWidth,
      rightWidth,
      onDrag,

      onOpenContextMenu,
      isRulerShow,
      canvasWarpStyle
    };
  }
});
