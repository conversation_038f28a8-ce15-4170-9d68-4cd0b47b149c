{"extends": "@hetu/tsconfig/tsconfig.app.json", "include": ["internal/**/*.ts", "packages/**/*.ts", "packages/**/*.d.ts", "packages/**/*.vue", "scripts/*.ts", "unocss.config.ts", "vite.config.ts", "package.json", "applications/visuo/src/pages/main/modules/design/utils/thumbnail.ts"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@hetu/acl": ["packages/acl/src"], "@hetu/auth": ["packages/auth/src"], "@hetu/core": ["packages/core/src"], "@hetu/http": ["packages/http/src"], "@hetu/theme": ["packages/theme/src"], "@hetu/util": ["packages/util/src"], "@hetu/platform-app": ["packages/app/src"], "@hetu/platform-boot": ["packages/boot/src"], "@hetu/platform-material": ["packages/material/src"], "@hetu/platform-cooperation": ["packages/cooperation/src"], "@hetu/platform-shared": ["packages/shared/src"], "@hetu/metadata-shared": ["package/metadata/src"], "@hetu/*": ["packages/*/src", "internal/*"]}}}