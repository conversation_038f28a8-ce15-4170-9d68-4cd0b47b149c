<template>
  <div class="vis-form-inline">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__label">尺寸</div>
        <div class="vis-form-field__content">
          <vis-button-group v-model="size" @update:modelValue="sizeChange" :options="sizeOptions"></vis-button-group>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./size.ts"></script>
