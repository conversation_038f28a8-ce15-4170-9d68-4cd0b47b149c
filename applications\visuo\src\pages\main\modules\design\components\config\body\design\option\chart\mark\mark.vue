<template>
  <div class="vis-form-inline">
    <div class="vis-form-inline__content--minus-32">
      <div class="vis-form-field">
        <div class="vis-form-field__content">
          <vis-fill v-model="fillPaints" :showEyes="false" :minusWidth="0" noImage></vis-fill>
        </div>
      </div>
    </div>
    <vis-mark-popup :options="options" isSeries>
      <template #btn>
        <q-btn flat class="btn-field">
          <ht-icon class="vis-icon" name="vis-control" />
        </q-btn>
      </template>
    </vis-mark-popup>
  </div>

  <component :is="`vis-config-${activeBlock.type}`" :options="options"></component>
</template>

<script lang="ts" src="./mark.ts"></script>
