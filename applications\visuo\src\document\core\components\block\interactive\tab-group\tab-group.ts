import {
  computed,
  defineComponent,
  ref,
  type CSSProperties,
  type PropType,
  onMounted,
  watch,
  nextTick,
  onBeforeUnmount
} from 'vue';
import {
  AxisField,
  Block,
  DisplayDirection,
  Direction,
  AroundPosition,
  ImageType,
  TabGroup,
  ResizeType,
  Text,
  TabStyle,
  Adorn,
  AdornType,
  type TabData,
  SelectMode,
  JustifyAlign,
  Align,
  TextEffects,
  SeniorFont
} from '../../../../models';
import { useBase, useFill, useUiStyle } from '../../../../hooks';
import { AttachmentService } from '@hetu/platform-shared';

enum Key {
  Adorn = 'adorn',
  FontStyle = 'fontStyle',
  TextEffects = 'textEffects',
  FillPaints = 'fillPaints',
  Stroke = 'stroke',
  Effects = 'effects'
}

/**
 * <AUTHOR>
 * 选项卡组件
 */
export default defineComponent({
  name: 'vis-tab-group',
  props: {
    widget: {
      type: Object as PropType<TabGroup>,
      required: true
    },
    block: {
      type: Object as PropType<Block>,
      required: true
    }
  },
  setup(props) {
    const { getStrokeStyle, getEffectStyle, getTextStyle, getTextEffectStyle, getTextOverflow } = useUiStyle();
    const { getFillPaintsStyle } = useFill();
    const { getWidgetFieldData, loadStaticData, fields } = useBase();

    const data = ref<TabData[]>([]);

    const tabBlock = computed(() => props.block);
    const tabOption = computed(() => props.widget.options);

    const layout = computed(() => tabOption.value.layout);
    const marqueeRefs = ref<HTMLElement[]>([]);

    const scrollRef = ref();

    const tabContentRef = ref<HTMLElement[]>([]);

    const changLineRefs = ref<HTMLElement[]>([]);

    const tabRef = ref<HTMLElement[]>([]);

    const hoverIndex = ref(-1);

    //#region 选中
    const getIndex = () => {
      if (typeof tabOption.value.defaultIndex === 'number') return [tabOption.value.defaultIndex - 1];
      if (tabOption.value.selectMode === SelectMode.Multiple) {
        // 多选
        return tabOption.value.defaultIndex
          .split(',')
          .map(Number)
          .filter((num) => !isNaN(num))
          .map((num) => num - 1);
      } else {
        // 单选
        const defaultArr = tabOption.value.defaultIndex.split(',');
        if (defaultArr.length) {
          // 取第一个
          return isNaN(Number(defaultArr[0])) ? [] : [Number(defaultArr[0]) - 1];
        } else {
          // 没有项返回空数组
          return [];
        }
      }
    };
    /**
     * 选中项
     */
    const selectedIndexs = ref(getIndex());

    /**
     * 偏移距离
     */
    const offset = computed(() => {
      if (!tabOption.value.autoPosition) return 0;
      const {
        direction,
        // gutter: [verticalGutter, horizontalGutter],
        horizontalGap,
        verticalGap,
        flowWrap
      } = layout.value;
      if (direction === DisplayDirection.Grid) return 0;
      const isHorizontal = direction === DisplayDirection.Horizontal;

      const wrapperSize = isHorizontal ? props.block.width : props.block.height;
      const tabSize = isHorizontal ? layout.value.width : layout.value.height;
      const gutter = isHorizontal ? horizontalGap : verticalGap;
      const resizeType = isHorizontal ? layout.value.resizeX : layout.value.resizeY;

      const activeIndex = selectedIndexs.value.length ? selectedIndexs.value[0] : 0;
      if (!data.value[activeIndex]) return 0;

      if (resizeType === ResizeType.Fixed) {
        if (flowWrap && isHorizontal) return 0;

        const allTabSize = tabSize * data.value.length + (data.value.length - 1) * gutter;
        if (allTabSize <= wrapperSize) return 0;

        if (wrapperSize < tabSize) {
          const selectedItemCenter = activeIndex * (tabSize + gutter) + tabSize / 2;
          return wrapperSize / 2 - selectedItemCenter;
        }

        if (activeIndex === 0) return 0;

        const selectedItemCenter = activeIndex * (tabSize + gutter) + tabSize / 2;
        const offset = wrapperSize / 2 - selectedItemCenter;

        const minOffset = 0;
        const maxOffset = wrapperSize - allTabSize;

        if (offset > minOffset) return minOffset;
        if (offset < maxOffset) return maxOffset;
        return offset;
      }

      return 0;
    });

    const handleActive = (idx: number) => {
      if (tabOption.value.selectMode === SelectMode.Multiple) {
        // 多选
        if (selectedIndexs.value.includes(idx)) {
          const index = selectedIndexs.value.findIndex((item) => item === idx);
          index != -1 && selectedIndexs.value.splice(index, 1);
        } else {
          selectedIndexs.value.push(idx);
        }
      } else {
        // 单选
        if (selectedIndexs.value.includes(idx)) return;
        selectedIndexs.value = [idx];

        if (tabOption.value.carousel) {
          // 设置点击停留
          isManualClick = true;
          clickEndTime = Date.now() + tabOption.value.carousel.clickTime * 1000;

          // 重新启动轮播（会优先使用clickTime）
          startCarousel();
        }
      }
    };

    watch([() => tabOption.value.defaultIndex, () => tabOption.value.selectMode], () => {
      selectedIndexs.value = getIndex();
    });

    watch(
      () => offset.value,
      (val) => {
        if (tabOption.value.layout.scrollbar) {
          const isHorizontal = layout.value.direction === DisplayDirection.Horizontal;
          scrollRef.value?.setScrollPosition(isHorizontal ? 'horizontal' : 'vertical', Math.abs(val), 300);
        }
      }
    );
    //#endregion 选中

    //#region 文本
    const getFontShow = (idx: number) => {
      if (hoverIndex.value === idx) {
        // 悬浮项
        // return tabOption.value.style.hover?tabOption.value.style.hover?.fontStyle.visible:;
        if (tabOption.value.style.hover.fontStyle) {
          return tabOption.value.style.hover.fontStyle.visible;
        } else if (selectedIndexs.value.includes(idx)) {
          return tabOption.value.style.active.fontStyle
            ? tabOption.value.style.active.fontStyle.visible
            : tabOption.value.style.fontStyle.visible;
        }
      } else if (selectedIndexs.value.includes(idx)) {
        return tabOption.value.style.active.fontStyle
          ? tabOption.value.style.active.fontStyle.visible
          : tabOption.value.style.fontStyle.visible;
      }
      return tabOption.value.style.fontStyle.visible;
    };
    //#endregion 文本

    /**
     * 跑马灯滚动速度值
     */
    const SPEED = 30;

    /**
     * 存储每个tab的是否溢出
     */
    const textOverflowFlags = ref<boolean[]>(Array((props.widget as any)?.staticData?.length || 0).fill(false));

    const allStyle = computed(() => {
      // 容器是否设置边框
      const hasStroke = props.block.stroke;
      // 容器边框宽度
      const strokeWidth = props.block.stroke ? props.block.stroke.position : [0, 0, 0, 0];

      const heightResize = layout.value.resizeY === ResizeType.Adapt;
      const widthResize = layout.value.resizeX === ResizeType.Adapt;

      return {
        height: heightResize ? '100%' : `calc( 100% - ${hasStroke ? strokeWidth[0] + strokeWidth[2] : 0}px )`,
        width: widthResize ? '100%' : `calc( 100% - ${hasStroke ? strokeWidth[1] + strokeWidth[3] : 0}px )`,
        marginLeft: widthResize ? '0px' : hasStroke ? (strokeWidth[1] + strokeWidth[3]) / 2 + 'px' : '0px',
        marginTop: heightResize ? '0px' : hasStroke ? (strokeWidth[0] + strokeWidth[2]) / 2 + 'px' : '0px'
      } as CSSProperties;
    });

    const tabStyle = computed(() => {
      let grid;
      if (layout.value.direction === DisplayDirection.Grid) {
        grid = {
          display: 'grid',
          gridTemplateColumns: `repeat(${layout.value.column}, ${
            layout.value.resizeX === ResizeType.Adapt ? 'minmax(0, 1fr)' : layout.value.width + 'px'
          })`,
          gridTemplateRows: `repeat(${layout.value.row},${
            layout.value.resizeY === ResizeType.Adapt ? 'minmax(0, 1fr)' : layout.value.height + 'px'
          })`,
          gridColumnGap: layout.value.horizontalGap + 'px',
          columnGap: layout.value.horizontalGap + 'px',
          gridRowGap: layout.value.verticalGap + 'px',
          rowGap: layout.value.verticalGap + 'px'
        };
      } else if (layout.value.direction === DisplayDirection.Horizontal) {
        if (layout.value.flowWrap && layout.value.resizeX === ResizeType.Fixed) {
          //  行列间距都生效
          grid = {
            display: 'grid',
            gridTemplateColumns: `repeat(auto-fill, ${layout.value.width}px)`,
            gridColumnGap: layout.value.horizontalGap + 'px',
            columnGap: layout.value.horizontalGap + 'px',
            gridRowGap: layout.value.verticalGap + 'px',
            rowGap: layout.value.verticalGap + 'px',
            gridAutoRows: layout.value.resizeY === ResizeType.Adapt ? 'auto' : layout.value.height + 'px'
          };
        } else {
          // 只有列间距
          grid = {
            display: 'grid',
            gridAutoFlow: 'column',
            gridTemplateRows: `repeat(1,${
              layout.value.resizeY === ResizeType.Adapt ? 'minmax(0, 1fr)' : layout.value.height + 'px'
            })`,
            gridColumnGap: layout.value.horizontalGap + 'px',
            columnGap: layout.value.horizontalGap + 'px',
            gridAutoColumns: layout.value.resizeX === ResizeType.Adapt ? '1fr' : layout.value.width + 'px'
          };
        }
      } else if (layout.value.direction === DisplayDirection.Vertical) {
        // 说明垂直排布
        grid = {
          display: 'grid',
          gridTemplateColumns: `repeat(1, ${
            layout.value.resizeX === ResizeType.Adapt ? 'minmax(0, 1fr)' : layout.value.width + 'px'
          })`,
          gridRowGap: layout.value.verticalGap + 'px',
          rowGap: layout.value.verticalGap + 'px',
          gridAutoRows: layout.value.resizeY === ResizeType.Adapt ? '1fr' : layout.value.height + 'px'
        };
      }
      return {
        height:
          layout.value.resizeX === ResizeType.Fixed &&
          layout.value.scrollbar &&
          layout.value.resizeY === ResizeType.Adapt
            ? props.block.height + 'px'
            : '100%',
        ...grid
      };
    });

    const tabItemStyle = (index: number) => {
      const style = tabOption.value.style;
      const width = layout.value.resizeX === ResizeType.Adapt ? '100%' : layout.value.width + 'px';
      const height = layout.value.resizeY === ResizeType.Adapt ? '100%' : layout.value.height + 'px';
      let fill, stroke, effects, isRow;
      if (hoverIndex.value === index && style.hover.visible) {
        // 悬浮样式处理
        // 说明有样式
        // fill = getFillPaintsStyle(
        //   style.hover.fillPaints ? [style.hover.fillPaints] : style.fillPaints ? [style.fillPaints] : undefined
        // );
        if (style.hover.fillPaints) {
          fill = getFillPaintsStyle([style.hover.fillPaints]);
          stroke = getStrokeStyle(style.hover.stroke);
          effects = getEffectStyle(style.hover.effects);
        } else if (selectedIndexs.value.includes(index)) {
          if (style.active.fillPaints) {
            fill = getFillPaintsStyle([style.active.fillPaints]);
            stroke = getStrokeStyle(style.active.stroke);
            effects = getEffectStyle(style.active.effects);
          } else {
            fill = getFillPaintsStyle(style.fillPaints ? [style.fillPaints] : undefined);
            stroke = getStrokeStyle(style.stroke);
            effects = getEffectStyle(style.effects);
          }
        } else {
          fill = getFillPaintsStyle(style.fillPaints ? [style.fillPaints] : undefined);
          stroke = getStrokeStyle(style.stroke);
          effects = getEffectStyle(style.effects);
        }

        if (style.hover.adorn) {
          isRow =
            style.hover.adorn?.position === AroundPosition.Left || style.hover.adorn?.position === AroundPosition.Right
              ? true
              : false;
        } else if (selectedIndexs.value.includes(index)) {
          if (style.active.adorn) {
            isRow =
              style.active.adorn.position === AroundPosition.Left ||
              style.active.adorn.position === AroundPosition.Right
                ? true
                : false;
          } else {
            isRow =
              tabOption.value.adorn.position === AroundPosition.Left ||
              tabOption.value.adorn.position === AroundPosition.Right
                ? true
                : false;
          }
        } else {
          isRow =
            tabOption.value.adorn.position === AroundPosition.Left ||
            tabOption.value.adorn.position === AroundPosition.Right
              ? true
              : false;
        }
      } else if (selectedIndexs.value.includes(index) && style.active.visible) {
        // 选中样式处理
        // fill = getFillPaintsStyle(style.active.fillPaints ? [style.active.fillPaints] : undefined);
        if (style.active.fillPaints) {
          fill = getFillPaintsStyle([style.active.fillPaints]);
          stroke = getStrokeStyle(style.active.stroke);
          effects = getEffectStyle(style.active.effects);
        } else {
          fill = getFillPaintsStyle(style.fillPaints ? [style.fillPaints] : undefined);
          stroke = getStrokeStyle(style.stroke);
          effects = getEffectStyle(style.effects);
        }

        if (style.active.adorn) {
          isRow =
            style.active.adorn?.position === AroundPosition.Left ||
            style.active.adorn?.position === AroundPosition.Right
              ? true
              : false;
        } else {
          isRow =
            tabOption.value.adorn.position === AroundPosition.Left ||
            tabOption.value.adorn.position === AroundPosition.Right
              ? true
              : false;
        }
      } else {
        // 普通样式处理
        fill = getFillPaintsStyle(style.fillPaints ? [style.fillPaints] : undefined);
        stroke = getStrokeStyle(style.stroke);
        effects = getEffectStyle(style.effects);

        isRow = tabOption.value.adorn
          ? tabOption.value.adorn.position === AroundPosition.Left ||
            tabOption.value.adorn.position === AroundPosition.Right
            ? true
            : false
          : true;
      }

      // 删除未设置样式
      for (const key in fill) {
        if (fill[key] === 'initial') {
          delete fill[key];
        }
      }

      for (const key in stroke) {
        if (stroke[key] === 'initial') {
          delete stroke[key];
        }
      }

      for (const key in effects) {
        if (effects[key] === 'initial') {
          delete effects[key];
        }
      }

      const fontStyle = getHierarchicalConfig(index, Key.FontStyle) as Text;

      // 文本溢出处理
      return {
        ...fill,
        ...stroke,
        ...effects,
        width,
        height,
        borderRadius: style.radius.map((r) => r + 'px').join(' '),
        display: 'flex',
        flexDirection: isRow ? 'row' : 'column',
        // 水平对齐
        [isRow ? 'justifyContent' : 'alignItems']:
          fontStyle.alignHorizontal === JustifyAlign.Center
            ? 'center'
            : fontStyle.alignHorizontal === JustifyAlign.Start
            ? 'flex-start'
            : fontStyle.alignHorizontal === JustifyAlign.End
            ? 'flex-end'
            : 'space-around',
        // 垂直对齐
        [isRow ? 'alignItems' : 'justifyContent']:
          fontStyle.alignVertical === Align.Center
            ? 'center'
            : fontStyle.alignVertical === Align.Start
            ? 'flex-start'
            : 'flex-end',
        transform: `translate${layout.value.direction === DisplayDirection.Horizontal ? 'X' : 'Y'}(${
          !layout.value.scrollbar ? offset.value : 0
        }px)`,
        transition: 'transform 0.3s ease'
      } as CSSProperties;
    };

    const tabContentStyle = (index: number) => {
      const style = tabOption.value.style;

      let textStyle, textShadow, isFullHeight, isFullWidth;
      let allHeight = 0;
      let allWidth = 0;

      // 处理不同状态下的样式
      if (hoverIndex.value === index && style.hover.visible) {
        ({ textStyle, textShadow, isFullHeight, isFullWidth, allHeight, allWidth } = getStatusStyle(
          'hover',
          style,
          index
        ));
      } else if (selectedIndexs.value.includes(index) && style.active.visible) {
        ({ textStyle, textShadow, isFullHeight, isFullWidth, allHeight, allWidth } = getStatusStyle(
          'active',
          style,
          index
        ));
      } else {
        const hasIcon = checkedStatusHasIcon(index);
        textStyle = getTextStyle(style.fontStyle as unknown as Text, style.fontStyle.direction === Direction.Vertical);
        textShadow = getTextEffectStyle(style.fontStyle.visible, style.textEffects);

        isFullHeight = hasIcon ? isHorizontalPosition(tabOption.value.adorn.position) : true;

        isFullWidth = hasIcon ? isVerticalPosition(tabOption.value.adorn.position) : true;
      }

      // 清理样式中的初始值
      cleanInitialValues(textStyle);
      cleanInitialValues(textShadow);

      const text = getHierarchicalConfig(index, Key.FontStyle) as Text;
      const { justifyContent, alignItems } = getTextStyle(text, style.fontStyle.direction === Direction.Vertical);

      return {
        ...overflowStyle.value,
        ...textStyle,
        ...textShadow,
        justifyContent: style.overflow === 3 && textOverflowFlags.value[index] ? 'flex-start' : justifyContent,
        alignItems,
        writingMode: style.fontStyle.direction === Direction.Horizontal ? 'horizontal-tb' : 'vertical-lr',
        textOrientation: 'upright',
        flex: dealOverflow(index),
        // height: getHeightValue(index, isFullHeight, allHeight),
        // width: getWidthValue(index, isFullWidth, allWidth),
        borderRadius: '0px'
      } as CSSProperties;
    };

    /**
     * 获取状态样式
     * @param status
     * @param style
     * @returns
     */
    const getStatusStyle = (status: 'hover' | 'active', style: TabStyle, idx: number) => {
      const statusStyle = style[status];

      const hasIcon = !!checkedStatusHasIcon(idx, status);
      const fontStyle = getHierarchicalConfig(idx, Key.FontStyle) as Text;
      const textStyle = getTextStyle(fontStyle, style.fontStyle.direction === Direction.Vertical);
      const adorn = getHierarchicalConfig(idx, Key.Adorn) as Adorn;
      const textEffects = getHierarchicalConfig(idx, Key.TextEffects) as TextEffects;
      const textShadow = getTextEffectStyle(!!statusStyle.fontStyle?.visible, textEffects, status);
      const isFullHeight = hasIcon ? isHorizontalPosition(adorn.position) : true;
      const isFullWidth = hasIcon ? isVerticalPosition(adorn.position) : true;

      // 计算图标尺寸
      const { allHeight, allWidth } = calculateIconDimensions(adorn, hasIcon);

      return { textStyle, textShadow, isFullHeight, isFullWidth, allHeight, allWidth };
    };

    /** 计算图标尺寸 */
    const calculateIconDimensions = (iconConfig: Adorn, hasIcon: boolean) => {
      if (!hasIcon) return { allHeight: 0, allWidth: 0 };

      const type = iconConfig.type;
      const height = type === AdornType.Image ? iconConfig.image.height : iconConfig.icon.size;
      const width = type === AdornType.Image ? iconConfig.image.width : iconConfig.icon.size;
      const gutter = iconConfig.gap;

      const allHeight = height + (isVerticalPosition(iconConfig.position) ? gutter : 0);
      const allWidth = width + (isHorizontalPosition(iconConfig.position) ? gutter : 0);

      return { allHeight, allWidth };
    };

    /**
     * 判断图标位置是否为水平方向
     * @param position
     * @returns
     */
    const isHorizontalPosition = (position: AroundPosition) => {
      return position === AroundPosition.Left || position === AroundPosition.Right;
    };

    /**
     * 判断图标位置是否为垂直方向
     * @param position
     * @returns
     */
    const isVerticalPosition = (position: AroundPosition) => {
      return position === AroundPosition.Top || position === AroundPosition.Bottom;
    };

    /**
     * 清理初始值
     * @param styleObj
     */
    const cleanInitialValues = (styleObj: any) => {
      for (const key in styleObj) {
        if (styleObj[key] === 'initial') {
          delete styleObj[key];
        }
      }
    };

    const dealOverflow = (index: number) => {
      const adorn = getHierarchicalConfig(index, Key.Adorn) as Adorn;
      const hasIcon = !!(adorn.type === AdornType.Image ? adorn.image.url : adorn.icon.name);
      if (textOverflowFlags.value[index]) {
        return hasIcon ? '1' : '0 1 auto';
      }
      if (tabOption.value.style.overflow === 1) {
        if (tabOption.value.style.fontStyle.direction === Direction.Horizontal) {
          return marqueeRefs.value[index] && marqueeRefs.value[index].scrollWidth > marqueeRefs.value[index].clientWidth
            ? '1'
            : '0 1 auto';
        } else {
          return '0 1 auto';
        }
      } else if (tabOption.value.style.overflow === 2) {
        // 换行
        if (tabOption.value.style.fontStyle.direction === Direction.Horizontal) {
          // 文本水平 检测高度对比
          if (marqueeRefs.value[index] && changLineRefs.value[index]) {
            return marqueeRefs.value[index].clientHeight > changLineRefs.value[index].clientHeight ? '1' : '0 1 auto';
          } else {
            return '0 1 auto';
          }
        } else {
          // 文本垂直 检测宽度对比

          return '0 1 auto';
        }
      }
    };

    const overflowStyle = computed(() => {
      const style = tabOption.value.style;
      const overflow = getTextOverflow(style.overflow);
      return overflow as CSSProperties;
    });

    /**
     * 检测是否添加图标配置
     * @param status
     * @returns
     */
    const checkedStatusHasIcon = (idx: number, status?: 'active' | 'hover') => {
      if (!status) {
        if (tabOption.value.adorn.type === AdornType.Image) {
          return tabOption.value.adorn.image.url;
        } else {
          return tabOption.value.adorn.icon.name;
        }
      }
      const isShowIcon = isShowStatus(idx, status);
      return isShowIcon;
    };

    // 检测文本是否溢出
    const checkTextOverflow = () => {
      nextTick(() => {
        if (!marqueeRefs.value || marqueeRefs.value.length === 0) return;

        // 确保textOverflowFlags数组长度与data相同
        if (textOverflowFlags.value.length !== data.value.length) {
          textOverflowFlags.value = Array(data.value.length).fill(false);
        }

        // 遍历所有元素检查溢出
        marqueeRefs.value.forEach((el, index) => {
          if (!el) return;
          const parentEl = el.parentElement;
          if (!parentEl) return;
          // 检查元素是否溢出
          const isOverflowing =
            tabOption.value.style.fontStyle.direction === Direction.Horizontal
              ? el.clientWidth > parentEl.clientWidth + 2
              : el.clientHeight > parentEl.clientHeight + 2;
          textOverflowFlags.value[index] = isOverflowing;
        });

        // 强制更新
        textOverflowFlags.value = [...textOverflowFlags.value];
      });
    };

    // 获取每个项目的溢出样式
    const getItemOverflowStyle = (index: number) => {
      const style = tabOption.value.style;
      // 确保索引有效
      if (index < 0 || index >= (textOverflowFlags.value.length || 0)) {
        return getTextOverflow(style.overflow) as CSSProperties;
      }
      // 只有在overflow为3（跑马灯）且文本确实溢出时才应用动画
      if (style.overflow === 3 && textOverflowFlags.value[index]) {
        const speed = tabOption.value.style.scrollSpeed > 0 ? SPEED * tabOption.value.style.scrollSpeed : SPEED;
        let duration = SPEED;
        if (marqueeRefs.value[index]) {
          const length =
            style.fontStyle.direction === Direction.Horizontal
              ? marqueeRefs.value[index].clientWidth
              : marqueeRefs.value[index].clientHeight;
          duration = length / speed;
        }
        return {
          display: 'inline-block',
          animation: `${
            style.fontStyle.direction === Direction.Horizontal ? Direction.Horizontal : Direction.Vertical
          }-scroll ${duration}s linear infinite`,
          paddingRight: (style.fontStyle.direction === Direction.Horizontal ? 5 : 0) + 'px',
          paddingBottom: (style.fontStyle.direction === Direction.Horizontal ? 0 : 5) + 'px'
        };
      }
      return getTextOverflow(style.overflow) as CSSProperties;
    };

    // 监听数据变化和样式变化，重新检测溢出
    watch(
      [() => data.value, () => props.block.width, () => props.block.height, () => tabOption.value],
      () => {
        checkTextOverflow();
      },
      { deep: true }
    );

    // 监听布局
    watch([() => tabOption.value.layout.direction], (val) => {
      const blockWidth = props.block.width;
      const blockHeight = props.block.height;
      if (val[0] === DisplayDirection.Horizontal) {
        // 切换为水平 区分情况
        if (layout.value.resizeX === ResizeType.Adapt) {
          // 宽度为自适应 比较块的宽高，值大的为宽度，值小的为高度
          if (layout.value.resizeY === ResizeType.Adapt) {
            tabBlock.value.height = blockWidth > blockHeight ? blockHeight : blockWidth;
          } else {
            tabBlock.value.height = layout.value.height;
          }
          tabBlock.value.width = blockWidth > blockHeight ? blockWidth : blockHeight;
        } else {
          // 宽度为固定值的情况下，块的宽度应是 选项卡的数量*宽度+ 横向间距*(选项卡数量-1)
          tabBlock.value.width =
            data.value.length * layout.value.width + (data.value.length - 1) * tabOption.value.layout.horizontalGap;

          // 高度自适应
          tabBlock.value.height = layout.value.resizeY === ResizeType.Adapt ? layout.value.width : layout.value.height;
        }
      } else if (val[0] === DisplayDirection.Vertical) {
        // 垂直显示
        if (layout.value.resizeY === ResizeType.Adapt) {
          if (layout.value.resizeX === ResizeType.Adapt) {
            // 行列都自适应的情况下块的宽高切换
            tabBlock.value.width = blockWidth > blockHeight ? blockHeight : blockWidth;
          } else {
            // 行高自适应  列宽有固定值
            tabBlock.value.width = layout.value.width;
          }
          tabBlock.value.height = blockWidth > blockHeight ? blockWidth : blockHeight;
        } else {
          tabBlock.value.width = layout.value.resizeX === ResizeType.Adapt ? layout.value.height : layout.value.width;

          // 高度固定高度的情况下
          tabBlock.value.height =
            data.value.length * layout.value.height + (data.value.length - 1) * tabOption.value.layout.verticalGap;
        }
      } else {
        // 网格处理
        // 获取最优行列数
        const { cols, rows } = calculateOptimalGrid(data.value.length);
        tabOption.value.layout.column = cols;
        tabOption.value.layout.row = rows;
        // 行列都自适应 都按照每个tab 宽度为100px 高度为50px 进行计算 有固定值则按照固定值计算
        const width = layout.value.resizeX === ResizeType.Adapt ? 100 : layout.value.width;
        const height = layout.value.resizeY === ResizeType.Adapt ? 50 : layout.value.height;
        tabBlock.value.width = cols * width + (cols - 1) * tabOption.value.layout.horizontalGap;
        tabBlock.value.height = rows * height + (rows - 1) * tabOption.value.layout.verticalGap;
      }
    });

    const calculateOptimalGrid = (length: number) => {
      if (length <= 0) return { rows: 0, cols: 0 };

      let bestRows = 1;
      let bestCols = length;
      let minDiff = Math.abs(bestRows - bestCols);

      // 从1到平方根遍历可能的行数
      for (let rows = 2; rows <= Math.sqrt(length); rows++) {
        const cols = Math.ceil(length / rows);
        const diff = Math.abs(rows - cols);

        // 如果找到更小的差值，更新最佳行列数
        if (diff < minDiff) {
          minDiff = diff;
          bestRows = rows;
          bestCols = cols;
        }
      }

      // 检查是否交换行列能得到更小的差值
      if (Math.abs(bestCols - bestRows) > Math.abs(bestRows - bestCols)) {
        [bestRows, bestCols] = [bestCols, bestRows];
      }

      return {
        rows: bestRows,
        cols: bestCols
      };
    };

    //#region 图标

    /**
     * 获取层级配置属性
     * @param idx 当前索引
     * @param propertyKey 要获取的属性键名
     * @returns 对应的属性值
     */

    const getHierarchicalConfig = (idx: number, propertyKey: Key) => {
      // 判断当前tab的状态
      const isHovered = hoverIndex.value === idx;
      const isSelected = selectedIndexs.value.includes(idx);

      // 层级配置优先级：hover > active > default
      const hoverConfig = tabOption.value.style.hover[propertyKey];
      if (isHovered) {
        // 悬浮状态优先

        if (hoverConfig) {
          return hoverConfig;
        }

        // 如果悬浮状态没有配置，但当前是选中状态，则检查active配置
        if (isSelected) {
          const activeConfig = tabOption.value.style.active?.[propertyKey];
          if (activeConfig) {
            return activeConfig;
          }
        }
      } else if (isSelected) {
        // 选中状态（非悬浮）
        const activeConfig = tabOption.value.style.active?.[propertyKey];
        if (activeConfig) {
          return activeConfig;
        }

        if (isHovered) {
          if (hoverConfig) {
            return hoverConfig;
          }
        }
      }

      // 默认配置 - 根据属性键名区分来源
      if (propertyKey === 'adorn') {
        // adorn属性从tabOption.value中获取
        return tabOption.value[propertyKey];
      } else {
        // 其他属性从tabOption.value.style中获取
        return tabOption.value.style[propertyKey];
      }
    };

    const getIconStyle = (idx: number, type: 'picture' | 'icon', status?: 'active' | 'hover') => {
      let position = AroundPosition.Right;
      const icon = getHierarchicalConfig(idx, Key.Adorn) as Adorn;

      if (icon.position === AroundPosition.Right) {
        position = AroundPosition.Left;
      } else if (icon.position === AroundPosition.Top) {
        position = AroundPosition.Bottom;
      } else if (icon.position === AroundPosition.Bottom) {
        position = AroundPosition.Top;
      }
      const { r, g, b, a } = icon?.icon.fillPaints.color || {};
      return {
        [`margin-${position}`]: icon.gap + 'px',
        width: (type === 'picture' ? icon.image.width : icon.icon.size) + 'px',
        height: (type === 'picture' ? icon.image.height : icon.icon.size) + 'px',
        fill: `rgba(${r}, ${g}, ${b}, ${a})`
      };
    };

    const isImage = (idx: number) => {
      const adorn = getHierarchicalConfig(idx, Key.Adorn) as Adorn;
      return adorn.type === AdornType.Image;
    };

    /**
     * 默认状态下的图标url
     */
    const iconUrl = computed(() => {
      if (!tabOption.value.adorn || !tabOption.value.adorn.image) return '';
      const { type, url } = tabOption.value.adorn.image;
      return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
    });

    const shouldShowIcon = (idx: number, position: string) => {
      const adorn = getHierarchicalConfig(idx, Key.Adorn) as Adorn;
      if (adorn.type === AdornType.None) return false;
      return (
        adorn.visible &&
        (adorn.type === AdornType.Image ? adorn.image.url : adorn.icon.name) &&
        showPositionIcon(position, adorn)
      );
    };

    const showPositionIcon = (position: string, adorn: Adorn) => {
      if (position === 'before') {
        return adorn.position === AroundPosition.Left || adorn.position === AroundPosition.Top ? true : false;
      } else {
        return adorn.position === AroundPosition.Right || adorn.position === AroundPosition.Bottom ? true : false;
      }
    };

    /**
     * 状态下的iconUrl
     */
    const statusIconUrl = (idx: number, status: 'hover' | 'active') => {
      if (status === 'hover') {
        if (tabOption.value.style[status].adorn) {
          if (tabOption.value.style[status].adorn.type === AdornType.Image) {
            const { type, url } = tabOption.value.style[status].adorn.image;
            return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
          } else {
            return tabOption.value.style[status].adorn.icon.name;
          }
        } else if (selectedIndexs.value.includes(idx)) {
          if (tabOption.value.style.active.adorn) {
            if (tabOption.value.style.active.adorn.type === AdornType.Image) {
              const { type, url } = tabOption.value.style.active.adorn.image;
              return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
            } else {
              return tabOption.value.style.active.adorn.icon.name;
            }
          } else {
            if (tabOption.value.adorn.type === AdornType.Image) {
              const { type, url } = tabOption.value.adorn.image;
              return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
            } else {
              return tabOption.value.adorn.icon.name;
            }
          }
        } else {
          if (tabOption.value.adorn.type === AdornType.Image) {
            const { type, url } = tabOption.value.adorn.image;
            return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
          } else {
            return tabOption.value.adorn.icon.name;
          }
        }
      } else if (status === 'active') {
        if (tabOption.value.style.active.adorn) {
          if (tabOption.value.style.active.adorn.type === AdornType.Image) {
            const { type, url } = tabOption.value.style.active.adorn.image;
            return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
          } else {
            return tabOption.value.style.active.adorn.icon.name;
          }
        } else {
          if (tabOption.value.adorn.type === AdornType.Image) {
            const { type, url } = tabOption.value.adorn.image;
            return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
          } else {
            return tabOption.value.adorn.icon.name;
          }
        }
      } else {
        if (tabOption.value.adorn.type === AdornType.Image) {
          const { type, url } = tabOption.value.adorn.image;
          return type === ImageType.File ? AttachmentService.downloadFileUrl(url) : url;
        } else {
          return tabOption.value.adorn.icon.name;
        }
      }
    };

    /**
     * 是否显示当前状态
     * @param idx
     * @param status
     * @returns
     */
    const isShowStatus = (idx: number, status: 'hover' | 'active') => {
      if (status === 'hover') {
        // 悬浮状态
        if (tabOption.value.style.hover.adorn) {
          return hoverIndex.value === idx && tabOption.value.style.hover.adorn.visible;
        }
        return false;
      }
      if (hoverIndex.value === idx) return false;

      if (tabOption.value.style.active.adorn) {
        return selectedIndexs.value.includes(idx) && tabOption.value.style.active.adorn.visible;
      }
      return false;
    };

    const getIcon = (idx: number, status: 'hover' | 'active') => {
      if (status === 'hover') {
        // 悬浮状态
        if (tabOption.value.style.hover.adorn) {
          return tabOption.value.style.hover.adorn.icon;
        } else if (selectedIndexs.value.includes(idx)) {
          if (tabOption.value.style.active.adorn) {
            return tabOption.value.style.active.adorn.icon;
          } else {
            return tabOption.value.adorn.icon;
          }
        } else {
          return tabOption.value.adorn.icon;
        }
      } else if (status === 'active') {
        if (tabOption.value.style.active.adorn) {
          return tabOption.value.style.active.adorn.icon;
        } else {
          return tabOption.value.adorn.icon;
        }
      } else {
        return tabOption.value.adorn.icon;
      }
    };

    //#endregion 图标

    //#region 数据
    const transformData = (originalData: any) => {
      return originalData.map((item: any) => {
        const newItem: { [key: string]: any } = {};

        ['rows', 'columns'].forEach((newKey) => {
          // 获取该 key 对应的映射配置（如 [{ fieldName: "A", ... }]）
          const mappingList = props.widget[newKey as keyof TabGroup] as AxisField[];

          // 如果该key有多个映射字段，则拼接这些字段的值
          if (mappingList.length > 1) {
            let concatenatedValue = '';
            mappingList.forEach((mapping) => {
              const originalField = mapping.fieldName;
              if (originalField && Object.prototype.hasOwnProperty.call(item, originalField)) {
                concatenatedValue += item[originalField];
              }
            });
            if (concatenatedValue) {
              newItem[newKey] = concatenatedValue;
            }
          }
          // 如果只有一个映射字段，则保持原逻辑
          else if (mappingList.length === 1) {
            const mapping = mappingList[0];
            const originalField = mapping.fieldName;
            if (originalField && Object.prototype.hasOwnProperty.call(item, originalField)) {
              newItem[newKey] = item[originalField];
            }
          }
        });

        return newItem;
      });
    };
    const loadWidgetData = async () => {
      return new Promise((resolve) => {
        if (props.widget.datasetType === 'static') {
          loadStaticData().then((res: any) => {
            if (res) {
              data.value = transformData(res);
            }
            resolve(res);
          });
          return;
        }
        getWidgetFieldData(
          new Promise((resolve) => {
            setTimeout(() => {
              console.log('ParagraphComponent: 重写加载选项卡数据', props.widget.id);
              resolve(true);
            }, 5000);
          })
        ).then((res) => {
          resolve(res);
        });
      });
    };
    //#endregion 数据

    //#region 自动轮播
    let timer: ReturnType<typeof setTimeout>;
    /**
     * 标记是否手动点击
     */
    let isManualClick = false;
    /** 点击停留结束时间戳 */
    let clickEndTime = 0;

    const startCarousel = () => {
      timer && clearInterval(timer);

      const interval =
        isManualClick && Date.now() < clickEndTime
          ? tabOption.value.carousel!.clickTime * 1000
          : tabOption.value.carousel!.interval * 1000;

      timer = setInterval(() => {
        // 如果在点击停留期内则不切换
        if (Date.now() < clickEndTime) return;
        // 重置手动标记
        isManualClick = false;
        const nextIndex = data.value[selectedIndexs.value[0] + 1] ? selectedIndexs.value[0] + 1 : 0;
        selectedIndexs.value = [nextIndex];

        // 使用正常间隔启动下一次轮播
        startCarousel();
      }, interval);
    };

    watch(
      [() => tabOption.value.carousel, () => tabOption.value.selectMode],
      ([carousel, selectMode]) => {
        // 清除旧定时器
        timer && clearInterval(timer);

        if (carousel && selectMode === SelectMode.Single) {
          startCarousel();
        }
      },
      { immediate: true, deep: true }
    );
    //#endregion 自动轮播

    onMounted(() => {
      // 确保DOM渲染完成后再检测
      checkTextOverflow();
    });

    onBeforeUnmount(() => {
      timer && clearInterval(timer);
    });

    return {
      tabOption,
      data,
      tabStyle,
      layout,
      ResizeType,
      overflowStyle,
      marqueeRefs,
      scrollRef,
      tabContentRef,
      tabRef,
      changLineRefs,
      textOverflowFlags,
      iconUrl,
      hoverIndex,
      selectedIndexs,
      allStyle,
      AdornType,
      getIconStyle,
      tabItemStyle,
      tabContentStyle,
      getItemOverflowStyle,
      shouldShowIcon,
      statusIconUrl,
      handleActive,
      loadWidgetData,
      getFontShow,
      getIcon,
      isShowStatus,
      isImage
    };
  }
});
