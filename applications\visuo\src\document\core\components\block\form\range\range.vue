<template>
  <div class="flex h-full items-center !flex-nowrap vis-form vis-slider" :class="[{ column: topPosition }]">
    <!-- 标签 -->
    <div
      v-if="computedOptions.label?.visible"
      class="vis-form__label"
      :class="topPosition ? 'w-full' : 'h-full'"
      :style="labelStyle"
    >
      <span>{{ computedOptions.label.text }}</span>
    </div>
    <div
      ref="contentRef"
      class="vis-form__content flex-1 fit flex !flex-nowrap items-center justify-between"
      :class="[topPosition ? 'w-full' : 'h-full', { 'overflow-hidden': labelInline }]"
      :style="style"
    >
      <div class="h-full flex !flex-nowrap overflow-hidden">
        <div v-if="fixAndStyle.prefixText" class="flex items-center px-2" :style="fixAndStyle.prefixTextStyle">
          {{ fixAndStyle.prefixText.text }}
        </div>
        <div v-if="fixAndStyle.prefixIcon" class="flex items-center px-2">
          <vis-svg-icon :icon="fixAndStyle.prefixIcon.icon" :style="fixAndStyle.prefixIconStyle" />
        </div>
      </div>
      <q-range
        v-model="inputValue"
        @update:modelValue="onValueChange"
        :min="computedOptions.min"
        :max="computedOptions.max"
        :step="computedOptions.step"
        :style="style"
        :label="!labelInline"
        :left-label-value="labelValue.min"
        :right-label-value="labelValue.max"
        dense
        class="flex-1"
      />

      <div class="thumb-label" :class="{ 'thumb-label__top absolute right-0 top-0': !labelInline }">
        {{ labelValue.min }} - {{ labelValue.max }}
      </div>
      <div class="h-full flex !flex-nowrap overflow-hidden">
        <div v-if="fixAndStyle.suffixIcon" class="flex items-center px-2">
          <vis-svg-icon :icon="fixAndStyle.suffixIcon.icon" :style="fixAndStyle.suffixIconStyle" />
        </div>
        <div v-if="fixAndStyle.suffixText" class="flex items-center px-2" :style="fixAndStyle.suffixTextStyle">
          {{ fixAndStyle.suffixText.text }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./range.ts"></script>
