import { Frame, GraphType, ItemsAlign, LayoutGuide, LayoutGuideType } from '@vis/document-core';
import { useDesignStore } from '../../../../../../stores';
import { computed, defineComponent, nextTick, ref } from 'vue';

/**
 * 布局网格
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-layout-guide',
  props: {},
  setup(props) {
    const designStore = useDesignStore();

    const activeGraph = computed(() => designStore.active.value.graph as Frame);

    const layoutGuides = computed(() => activeGraph.value.layoutGuide);

    const types = [
      { label: '网格', value: LayoutGuideType.Grid },
      { label: '栅格行', value: LayoutGuideType.Row },
      { label: '栅格列', value: LayoutGuideType.Column }
    ];

    const patternModeCols = [
      { label: '左侧', value: ItemsAlign.Start },
      { label: '居中', value: ItemsAlign.Center },
      { label: '右侧', value: ItemsAlign.End },
      { label: '拉抻', value: ItemsAlign.Stretch }
    ];

    const patternModeRows = [
      { label: '顶部', value: ItemsAlign.Start },
      { label: '居中', value: ItemsAlign.Center },
      { label: '底部', value: ItemsAlign.End },
      { label: '拉抻', value: ItemsAlign.Stretch }
    ];

    const popupRef = ref();
    const isShow = ref(false);

    const activeLayoutGuide = ref<LayoutGuide>();
    const activeIndex = ref<number>();

    /**
     * 打开布局弹窗
     * @param e
     * @param layoutGuide
     * @param index
     */
    const onShowPopup = (e: Event, layoutGuide: LayoutGuide, index: number) => {
      activeLayoutGuide.value = layoutGuide;
      activeIndex.value = index;
      isShow.value = !isShow.value;
      nextTick(() => {
        popupRef.value?.handleShow(e);
      });
    };

    /**
     * 关闭
     */
    const onHidePopup = () => {
      isShow.value = false;
      activeLayoutGuide.value = undefined;
      activeIndex.value = undefined;
    };

    const onChangeType = (value: LayoutGuideType) => {
      // 布局方式时，内部的图形删除gridItem(原因：切换类型时保留gridItem会照常内部容器的位置变化)
      if (activeGraph.value.children.length) {
        activeGraph.value.children.forEach((child) => {
          child.gridItem = undefined;
        });
      }
    };

    /**
     * 改变布局方式
     * @param value
     */
    const onChangePattern = (value: ItemsAlign) => {
      if (activeLayoutGuide.value) {
        const { width } = activeLayoutGuide.value;
        if (value === ItemsAlign.Stretch) {
          activeLayoutGuide.value.width = 'Auto';
        } else {
          activeLayoutGuide.value.width = width === 'Auto' ? 10 : width;
          if (value === ItemsAlign.Center) {
            activeLayoutGuide.value.offset = 0;
          }
        }

        // 布局方式改为不是拉伸的情况时，内部的图形删除gridItem
        if (activeGraph.value.children.length) {
          activeGraph.value.children.forEach((child) => {
            child.gridItem = undefined;
          });
        }
      }
    };

    const onDelete = (index: number) => {
      layoutGuides.value.splice(index, 1);
    };

    const onAdd = (event: MouseEvent) => {
      if (activeGraph.value.type === GraphType.Frame) {
        layoutGuides.value.push(new LayoutGuide());
        const i = layoutGuides.value.length - 1;
        onShowPopup(event, layoutGuides.value[i], i);
      }
    };

    return {
      activeGraph,
      layoutGuides,
      types,
      patternModeCols,
      patternModeRows,

      LayoutGuideType,
      ItemsAlign,

      popupRef,
      isShow,
      activeIndex,
      activeLayoutGuide,

      onShowPopup,
      onHidePopup,

      onChangePattern,
      onChangeType,

      onDelete,
      onAdd
    };
  }
});
