import { FillPaints, Stroke } from '../../..';
import { Axis, AxisGrid } from '../axis';
import { ChartOptions } from '../chart';
import { Mark } from '../mark';

/**
 * 柱状图标记
 * <AUTHOR>
 */
export class BarMark extends Mark {
  /**
   * 类型
   */
  type = 'rect';

  /**
   * 柱子宽度
   */
  width = 20;

  /**
   * 柱子最小高度
   */
  height = 0;

  /**
   * 类间距
   */
  gap = 0;

  /**
   * 组间距
   */
  groupGap = 0;

  /**
   * 柱子圆角
   */
  radius = [0, 0, 0, 0];

  /**
   * 分组/堆叠
   */
  showType = 'group';

  /**
   * 柱子背景
   */
  background: {
    fillPaints: FillPaints;
    stroke: Stroke;
  } = {
    fillPaints: new FillPaints(),
    stroke: new Stroke()
  };
}

/**
 * 柱状图配置
 * <AUTHOR>
 */
export class BarOptions extends ChartOptions {
  mark: BarMark = new BarMark();

  /** x轴 */
  xAxis: Axis = new Axis(new AxisGrid(0));

  /** y轴 */
  yAxis: Axis = new Axis(new AxisGrid(0));
}
