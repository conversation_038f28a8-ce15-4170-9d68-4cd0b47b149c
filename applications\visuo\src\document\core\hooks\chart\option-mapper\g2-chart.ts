import { Chart as G2 } from '@antv/g2';
import { markRaw } from 'vue';
import { markTypeMap } from './option-utils';

/**
 * G2 图表工具函数
 * <AUTHOR>
 */

/**
 * 创建图表实例
 */
export const createChart = (container: HTMLElement, options: any = {}): any => {
  try {
    const chart = new G2({
      container,
      autoFit: true,
      clip: true,
      padding: 'auto',
      inset: 0,
      ...options
    });

    return markRaw(chart);
  } catch (error) {
    console.error('创建图表实例失败:', error);
    throw error;
  }
};

/**
 * 创建图表视图
 */
export const createChartView = (chart: G2, data: any[] = []): any => {
  if (!chart) {
    throw new Error('图表实例未初始化');
  }

  try {
    const view = chart
      .view()
      .attr('autoFit', true)
      .attr('clip', true)
      .attr('padding', 'auto')
      .attr('inset', 0)
      .data(data);
    return view;
  } catch (error) {
    console.error('创建图表视图失败:', error);
    throw error;
  }
};

/**
 * 渲染图表
 */
export const renderChart = async (chart: G2): Promise<void> => {
  if (!chart) {
    throw new Error('图表实例未初始化');
  }

  try {
    await chart.render();
  } catch (error) {
    console.error('渲染图表失败:', error);
    throw error;
  }
};

/**
 * 更新图表数据
 */
export const updateChartData = (view: G2, data: any[]): void => {
  if (!view) {
    throw new Error('图表实例未初始化');
  }

  try {
    view.changeData(data);
  } catch (error) {
    console.error('更新图表数据失败:', error);
    throw error;
  }
};

/**
 * 调整图表大小
 */
export const resizeChart = (chart: G2, width: number, height: number): void => {
  if (!chart) {
    throw new Error('图表实例未初始化');
  }

  try {
    chart.changeSize(width, height);
  } catch (error) {
    console.error('调整图表大小失败:', error);
    throw error;
  }
};

/**
 * 销毁图表
 */
export const destroyChart = (chart: G2): void => {
  if (!chart) {
    return;
  }

  try {
    chart.destroy();
  } catch (error) {
    console.error('销毁图表失败:', error);
  }
};

/**
 * 清理视图
 */
export const clearChart = (chart: G2): void => {
  if (!chart) {
    return;
  }

  try {
    chart.clear();
  } catch (error) {
    console.warn('清理视图失败:', error);
  }
};

/**
 * 获取图表配置
 */
export const getChartOptions = (chart: G2): any => {
  if (!chart) {
    throw new Error('图表实例未初始化');
  }

  try {
    return chart.options();
  } catch (error) {
    console.error('获取图表配置失败:', error);
    throw error;
  }
};

/**
 * 创建图形
 */
export const createMark = (
  view: any,
  type: string,
  xField: string,
  yField: string,
  colorField?: string,
  seriesField?: string
) => {
  if (!view) {
    throw new Error('图表视图未初始化');
  }

  try {
    const mark = view[markTypeMap[type] as string]();

    // 设置编码
    mark.encode('x', xField);
    mark.encode('y', yField);

    if (colorField) {
      mark.encode('color', () => colorField);
    }

    if (seriesField) {
      mark.encode('series', () => seriesField);
    }

    return mark;
  } catch (error) {
    console.error('创建图形失败:', error);
    throw error;
  }
};

/**
 * 获取图表视图
 */
export const getChartView = (chart: G2): any => {
  if (!chart) {
    throw new Error('图表实例未初始化');
  }

  return chart.getView();
};