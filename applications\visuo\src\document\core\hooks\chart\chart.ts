import { ref, onUnmounted, getCurrentInstance, type ComponentPublicInstance, computed } from 'vue';
import type { Axis, BarMark, Block, Chart, Legend, Mark } from '../../models';
import {
  applyLegendConfig,
  mapAxisConfig,
  mapScaleConfig,
  applyAxisConfig,
  applyScaleConfig,
  createChart,
  createChartView,
  createMark,
  renderChart,
  updateChartData,
  resizeChart,
  destroyChart,
  clearChart,
  mapLegendConfig,
  mapMarkConfig,
  applyMarkStyle,
  getChartView,
  mapBarConfig,
  applyBarStyle
} from './';

/**
 * 图表 Hooks
 * <AUTHOR>
 */
export const useChart = () => {
  /**
   * 组件实例
   */
  const instance = getCurrentInstance();

  /**
   * 获取组件实例代理
   */
  const component = instance?.proxy as ComponentPublicInstance & {
    $props: { widget: Chart; block: Block; widgetData: any[] };
  };

  const widget = computed(() => component.$props.widget || null);
  const block = computed(() => component.$props.block || null);
  const widgetData = computed(() => component.$props.widgetData || []);
  const options = computed(() => JSON.parse(JSON.stringify(widget.value.options)));

  const chart = ref<any>();
  const view = ref<any>();
  const series = ref<any>();

  /**
   * 清理视图
   */
  const clear = () => {
    if (chart.value) {
      try {
        clearChart(chart.value);
        view.value = undefined;
      } catch (error) {
        console.error('清理视图失败:', error);
      }
    }
  };

  /**
   * 初始化图表
   */
  const init = async (container: HTMLElement, options: any = {}) => {
    try {
      if (chart.value) {
        destroy();
      }

      chart.value = createChart(container, options);
      return chart.value;
    } catch (error) {
      console.error('图表初始化失败:', error);
      throw error;
    }
  };

  const createSeries = (): any => {
    const newSeries: any[] = [];

    if (widget.value.xField[0]) {
      const xField = widget.value.xField[0]?.fieldName || '';
      widget.value.yField.length > 0 &&
        widget.value.yField.forEach((item: any) => {
          const newSerie = createMark(
            view.value,
            widget.value.type,
            xField,
            item.fieldName || '',
            item.fieldAlias || '',
            item.fieldName || ''
          );
          newSeries.push(newSerie);
        });
    }

    return newSeries;
  };

  /**
   * 配置图表
   */
  const configureChart = async (data: any[] = widgetData.value, config: any = options.value) => {
    if (!chart.value || !data) {
      return;
    }

    try {
      // 清理旧视图
      clear();
      // 创建视图
      view.value = createChartView(chart.value, data);

      series.value = createSeries();

      updateChartConfig(config, true);
    } catch (error) {
      console.error('配置图表失败:', error);
      throw error;
    }
  };

  /**
   * 更新X轴配置
   */
  const updateXAxisConfig = (xAxisConfig: Axis = <Axis>options.value?.xAxis, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    try {
      // 映射X轴配置
      const g2Config = mapAxisConfig(xAxisConfig, 'x');

      // 应用轴配置
      applyAxisConfig(view.value, 'x', g2Config.axis?.x);

      // 映射并应用比例尺配置
      const scaleConfig = mapScaleConfig(xAxisConfig, 'x');
      if (Object.keys(scaleConfig).length > 0) {
        applyScaleConfig(view.value, scaleConfig);
      }

      // X轴反转通过 scale 配置处理

      isRender && render();
    } catch (error) {
      console.error('更新X轴配置失败:', error);
      throw error;
    }
  };

  /**
   * 更新Y轴配置
   */
  const updateYAxisConfig = (yAxisConfig: Axis = <Axis>options.value?.yAxis, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    try {
      const g2Config = mapAxisConfig(yAxisConfig, 'y');

      // 应用轴配置
      applyAxisConfig(view.value, 'y', g2Config.axis?.y);

      const scaleConfig = mapScaleConfig(yAxisConfig, 'y');
      if (Object.keys(scaleConfig).length > 0) {
        applyScaleConfig(view.value, scaleConfig);
      }

      isRender && render();
    } catch (error) {
      console.error('更新Y轴配置失败:', error);
      throw error;
    }
  };

  /**
   * 更新图形样式配置
   */
  const updateMarkConfig = (markConfig: Mark = <Mark>options.value?.mark, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    try {
      // 为每个系列分别应用样式
      series.value.forEach((serie: any, index: number) => {
        const g2Config = mapMarkConfig(markConfig, options.value, index);

        // 应用图形配置
        applyMarkStyle(serie, g2Config);

        const g2BarConfig = mapBarConfig(markConfig as BarMark);

        // 应用柱状图配置
        applyBarStyle(serie, g2BarConfig);
      });

      isRender && render();
    } catch (error) {
      console.error('更新图形配置失败:', error);
      throw error;
    }
  };

  /**
   * 更新图例配置
   */
  const updateLegendConfig = (legendConfig: Legend = options.value.legend, isRender: boolean = true) => {
    if (!chart.value || !view.value) {
      return;
    }

    try {
      const g2Config = mapLegendConfig(legendConfig, options.value);

      applyLegendConfig(view.value, g2Config.legend);

      isRender && render();
    } catch (error) {
      console.error('更新图例配置失败:', error);
      throw error;
    }
  };

  /**
   * 更新图表配置（保持数据不变）
   * 根据传入的配置项，调用对应的专门更新方法
   */
  const updateChartConfig = (config: any = options.value, isRender: boolean = false) => {
    if (!chart.value) {
      return;
    }

    try {
      if (config.xAxis !== undefined) {
        updateXAxisConfig(config.xAxis, false);
      }

      if (config.yAxis !== undefined) {
        updateYAxisConfig(config.yAxis, false);
      }

      if (config.legend !== undefined) {
        updateLegendConfig(config.legend, false);
      }

      if (config.mark !== undefined) {
        updateMarkConfig(config.mark, false);
      }

      // 重新渲染
      isRender && render();
    } catch (error) {
      console.error('更新图表配置失败:', error);
      throw error;
    }
  };

  /**
   * 渲染图表
   */
  const render = async () => {
    if (!chart.value) {
      return;
    }
    await renderChart(chart.value);
  };

  /**
   * 更新图表数据
   */
  const updateData = (data: any[] = widgetData.value) => {
    if (!chart.value) {
      return;
    }
    updateSeries();
    updateChartConfig(options.value, true);
    updateChartData(view.value, data);
  };

  const updateSeries = () => {
    if (!chart.value) {
      return;
    }
    view.value.children = [];
    series.value = createSeries();
  };

  /**
   * 调整图表大小
   */
  const resize = (width: number = block.value?.width, height: number = block.value?.height) => {
    if (!chart.value) {
      return;
    }
    resizeChart(chart.value, width, height);
  };

  /**
   * 销毁图表
   */
  const destroy = () => {
    if (chart.value) {
      clear();
      destroyChart(chart.value);
      chart.value = undefined;
    }
  };

  const getSeries = (id?: string) => {
    if (!series.value) {
      return;
    }
    return id ? series.value.find((s: any) => s.id === id) : series.value;
  };

  const getView = () => {
    if (!chart.value) {
      return;
    }
    return getChartView(chart.value);
  };

  // 组件卸载时自动销毁图表
  onUnmounted(() => {
    destroy();
  });

  return {
    chart,
    view,
    series,
    init,
    configureChart,
    render,
    getView,
    getSeries,
    createSeries,
    updateSeries,
    updateData,
    updateChartConfig,
    updateXAxisConfig,
    updateYAxisConfig,
    updateMarkConfig,
    updateLegendConfig,
    resize,
    destroy
  };
};
