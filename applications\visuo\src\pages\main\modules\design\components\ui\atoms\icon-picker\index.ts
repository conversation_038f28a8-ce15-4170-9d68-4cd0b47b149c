import { computed, defineComponent, nextTick, ref, watch, type PropType } from 'vue';
import { IconConfig, useDocumentStore, type Icon } from '@vis/document-core';
import { scrollDomIntoView } from '@hetu/platform-shared';

/**
 * 图标组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-icon-picker',
  props: {
    modelValue: {
      type: Object as PropType<Icon>,
      required: true
    }
  },
  setup(props, { emit }) {
    const { iconConfigs } = useDocumentStore();

    const computedModel = computed({
      get() {
        return props.modelValue;
      },
      set(value) {
        emit('update:modelValue', value);
      }
    });

    const popupRef = ref();
    const showPopup = (e: Event) => {
      e.stopPropagation();

      filterName.value = '';
      filterActive.value = false;
      iconType.value = '';
      popupRef.value?.handleShow(e);

      nextTick(() => {
        // 滚动到当前选中
        if (computedModel.value.name) {
          const scrollDom = document.querySelector('.vis-icon-picker__list') as HTMLElement;
          const active = scrollDom.querySelector('.active') as HTMLElement;
          scrollDomIntoView(active, scrollDom);
        }
      });
    };

    const color = ref(computedModel.value.fillPaints);

    // #region 列表

    // 图标类型筛选
    const iconType = ref('');
    const typeMenuShow = ref(false);
    const iconTypeOptions = computed(() => {
      return iconConfigs.value.map((icon: IconConfig) => {
        return {
          label: icon.title,
          value: icon.name
        };
      });
    });
    const handleIconType = (value: string) => {
      iconType.value = value;
      typeMenuShow.value = false;
    };

    // 名称筛选
    const filterName = ref('');
    const filterActive = ref(false);
    const filterRef = ref();
    const handleFilter = () => {
      filterActive.value = true;
      nextTick(() => {
        filterRef.value?.focus();
      });
    };

    const handleFilterBlur = () => {
      if (!filterName.value) {
        filterActive.value = false;
      }
    };

    // 图标列表
    const iconList = computed(() => {
      let res: IconConfig[] = [];

      const newIconConfigs = JSON.parse(JSON.stringify(iconConfigs.value)) as IconConfig[];
      newIconConfigs.forEach((group: IconConfig) => {
        if (group.children) {
          group.children.map((i) => (i.type = group.name));
        }
      });

      // 根据类型过滤
      if (!iconType.value) {
        newIconConfigs.forEach((newGroup: IconConfig) => {
          if (!newGroup.children) return;

          res.push(...newGroup.children);
        });
      } else {
        res = newIconConfigs.find((icon: IconConfig) => icon.name === iconType.value)?.children || [];
      }

      // 根据名称过滤
      if (filterName.value) {
        res = res.filter(
          (icon: IconConfig) => icon.title.includes(filterName.value) || icon.name.includes(filterName.value)
        );
      }
      return res;
    });
    // 选择图标
    const handleSelect = (icon: IconConfig) => {
      Object.assign(computedModel.value, icon);
      emit('update:modelValue', computedModel.value);
    };

    const hidePopup = () => {
      emit('hide');
    };

    // #endregion
    return {
      computedModel,

      popupRef,
      showPopup,

      color,

      iconType,
      typeMenuShow,
      iconTypeOptions,
      handleIconType,

      filterName,
      filterActive,
      filterRef,
      handleFilter,
      handleFilterBlur,

      iconList,
      handleSelect,
      hidePopup
    };
  }
});
