<template>
  <div class="vis-text relative">
    <!-- 颜色 -->
    <div class="vis-form-inline" v-if="!mini">
      <div class="vis-form-field">
        <div v-if="fillLabel" class="vis-form-field__label">{{ fillLabel }}</div>
        <div class="vis-form-inline__content">
          <vis-fill v-model="computedOption.fillPaints" />
        </div>
      </div>
    </div>

    <!-- 字体 -->
    <div class="vis-form-inline">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__label" v-if="textTitle">{{ textTitle }}</div>
          <div class="vis-form-field__content" :class="{ 'vis-invisible': !visible }">
            <vis-select v-model="computedOption.fontFamily" :options="fontFamilys" :popHeight="240">
              <template #option="{ opt, itemProps }">
                <q-item class="flex items-center" v-bind="itemProps">
                  <q-item-section>
                    <span :style="{ fontWeight: computedOption.fontWeight, fontFamily: opt }">{{ opt }}</span>
                  </q-item-section>
                </q-item>
              </template>
            </vis-select>
          </div>
        </div>
      </div>
    </div>

    <div class="vis-form-inline" v-if="!mini">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <!-- 粗细 -->
            <vis-select v-model="computedOption.fontWeight" :options="fontWeights">
              <template #option="{ opt, itemProps }">
                <q-item class="flex items-center" v-bind="itemProps">
                  <q-item-section>
                    <span :style="{ fontWeight: opt.value, fontFamily: computedOption.fontFamily }">
                      {{ opt.label }}
                    </span>
                  </q-item-section>
                </q-item>
              </template>
            </vis-select>
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__content">
            <!-- 字号-->
            <vis-font-size v-model="computedOption.fontSize" noIcon />
          </div>
        </div>
      </div>
    </div>

    <div class="vis-form-inline" v-else>
      <div class="vis-form-inline__content--minus-32" :class="{ 'vis-invisible': !visible }">
        <div class="vis-form-field">
          <!-- 粗细 -->
          <vis-select v-model="computedOption.fontWeight" :options="fontWeights">
            <template #option="{ opt, itemProps }">
              <q-item class="flex items-center" v-bind="itemProps">
                <q-item-section>
                  <span :style="{ fontWeight: opt.value, fontFamily: computedOption.fontFamily }">
                    {{ opt.label }}
                  </span>
                </q-item-section>
              </q-item>
            </template>
          </vis-select>
        </div>
        <div class="vis-form-field">
          <div class="flex gap-2">
            <!-- 字号-->
            <vis-font-size class="flex-1" v-model="computedOption.fontSize" noIcon />

            <div class="w-6">
              <vis-text-more :text="option" :textEffects="textEffects" :stroke="stroke" />
            </div>
          </div>
        </div>
      </div>
      <!-- 显隐按钮 -->
      <q-btn class="vis-field--mini btn-field" flat @click="handleVisible">
        <ht-icon class="vis-icon" :name="visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
      </q-btn>
    </div>

    <div class="vis-form-inline" v-if="!isBase">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field" v-if="showOuter('lineHeight')">
          <div class="vis-form-field__label">行高</div>
          <div class="vis-form-field__content">
            <!-- 行高 -->
            <vis-number v-model="computedOption.lineHeight" icon="vis-resizing-h" :min="0" />
          </div>
        </div>

        <div class="vis-form-field" v-if="showOuter('letterSpacing')">
          <div class="vis-form-field__label">字距</div>
          <div class="vis-form-field__content">
            <!-- 字间距 -->
            <vis-number v-model="computedOption.letterSpacing" icon="vis-resizing-w" :min="0" />
          </div>
        </div>
      </div>
    </div>

    <div class="vis-form-inline" v-if="!isBase">
      <div class="vis-form-inline__content--minus-32">
        <div class="vis-form-field" v-if="showOuter('alignHorizontal')">
          <div class="vis-form-field__label">对齐</div>
          <div class="vis-form-field__content">
            <!-- 对齐方式 -->
            <vis-button-group v-model="computedOption.alignHorizontal" :options="alignHorizontalOptions" />
          </div>
        </div>
        <div class="vis-form-field" v-if="showOuter('adapt')">
          <div class="vis-form-field__content">
            <!-- 展示模式 -->
            <vis-button-group v-model="computedOption.adapt" :options="adaptOptions" />
          </div>
        </div>
      </div>
    </div>

    <slot></slot>
    <!-- 更多模式 -->
    <q-btn
      class="btn-field absolute bottom-0 right-0"
      v-if="isMore"
      :class="{ active: popupShow }"
      @click.stop="showPopup"
    >
      <ht-icon class="vis-icon" name="hticon-vis-control" />
    </q-btn>

    <!-- 弹窗设置 -->
    <vis-popup title="字体" ref="popupRef" :target="false" @hide="popupShow = false">
      <vis-preview :ishover="isHover" :option="hoverOption" />
      <div class="vis-form-inline">
        <!-- 字间距 -->
        <div class="vis-form-field" v-if="showSetting('letterSpacing')">
          <div class="vis-form-field__label">字间距</div>
          <div class="vis-form-field__content">
            <vis-number v-model="computedOption.letterSpacing" icon="vis-resizing-w" :min="0" />
          </div>
        </div>
        <!-- 行高 -->
        <div class="vis-form-field" v-if="showSetting('lineHeight')">
          <div class="vis-form-field__label">行高</div>
          <div class="vis-form-field__content">
            <vis-number v-model="computedOption.lineHeight" icon="vis-resizing-h" :min="0" />
          </div>
        </div>
        <!-- 对齐方式 -->
        <div class="vis-form-field" v-if="showSetting('alignHorizontal')">
          <div class="vis-form-field__label">对齐方式</div>
          <div class="vis-form-field__content">
            <vis-button-group
              v-model="computedOption.alignHorizontal"
              :options="alignHorizontalPopOptions"
              @handleHover="handlerHoverOption($event, 'alignHorizontal')"
            ></vis-button-group>
          </div>
        </div>
        <!-- 展示模式 -->
        <div class="vis-form-field" v-if="showSetting('adapt')">
          <div class="vis-form-field__label">展示模式</div>
          <div class="vis-form-field__content">
            <vis-button-group
              v-model="computedOption.adapt"
              :options="adaptPopOptions"
              @handleHover="handlerHoverOption($event, 'adapt')"
            ></vis-button-group>
          </div>
        </div>

        <!-- 斜体、下划线、删除线 -->
        <div class="vis-form-field" v-if="isFull || showSetting('fontStyle')">
          <div class="vis-form-field__label">文字修饰</div>
          <div class="vis-form-field__content">
            <div class="flex-1 flex btn-bg">
              <q-btn
                v-for="(item, index) in fontStyles"
                :key="item.value"
                dense
                flat
                size="xs"
                class="rounded-borders overflow-hidden flex-1"
                :class="[
                  { 'vis-btn-active': computedOption[fontStyleKeys[index]] },
                  { 'q-mr-xs': index === fontStyles.length - 1 }
                ]"
                :label="item.label"
                @click="setFontStyle(fontStyleKeys[index])"
                @mouseenter="handlerHoverOption(item.value, 'fontStyle')"
                @mouseleave="handlerHoverOption(false, 'fontStyle')"
              >
                <template v-if="item.icon">
                  <ht-icon v-if="isIconFont(item.icon)" :name="item.icon" class="vis-icon" />
                  <q-icon v-else :name="item.icon" />
                </template>
                <q-tooltip v-if="item.tip">{{ item.tip }}</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
        <!-- 垂直对齐 -->
        <div class="vis-form-field" v-if="isFull || showSetting('alignVertical')">
          <div class="vis-form-field__label">垂直对齐</div>
          <div class="vis-form-field__content">
            <vis-button-group
              v-model="computedOption.alignVertical"
              :options="alignVerticalOptions"
              @handleHover="handlerHoverOption($event, 'alignVertical')"
            ></vis-button-group>
          </div>
        </div>

        <!-- 首行缩进 -->
        <div class="vis-form-field" v-if="showSetting('textIndent')">
          <div class="vis-form-field__label">首行缩进</div>
          <div class="vis-form-field__content">
            <vis-number v-model="computedOption.textIndent" icon="format_indent_increase" :min="0" />
          </div>
        </div>
      </div>
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
