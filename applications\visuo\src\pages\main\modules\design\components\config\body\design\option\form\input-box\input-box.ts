import { FormFix, Label, type InputBoxOptions, FormRuleType, FormRule, FillPaints } from '@vis/document-core';
import { computed, defineComponent, ref, watch, type PropType } from 'vue';
import { useWidget } from '../../../../../../../hooks';
import { VisFormLabel, VisFormFix, VisFormSize, VisFormRules } from '../common';

/**
 * 文本输入组件属性面板
 * @authot guohuizheng
 */
export default defineComponent({
  name: 'vis-config-input-box-option',
  components: {
    VisFormLabel,
    VisFormFix,
    VisFormSize,
    VisFormRules
  },
  props: {
    options: {
      type: Object as PropType<InputBoxOptions>,
      required: true
    }
  },
  setup(props) {
    const { handleManage } = useWidget();
    const computedOptions = computed(() => props.options);

    const showMenuFocus = ref(false);
    const addFocus = () => {
      computedOptions.value.focusFillPaints = new FillPaints();
      showMenuFocus.value = false;
    };

    const delFocus = () => {
      delete computedOptions.value.focusFillPaints;
    };

    //#region 圆角
    /** 圆角 */
    const radius = ref<number | string>(computedOptions.value.radius[0]);

    const radiusChange = () => {
      if (typeof radius.value === 'number') {
        computedOptions.value.radius = [radius.value, radius.value, radius.value, radius.value];
      }
    };

    const showRadius = ref(false);

    const flag = computed(
      () => !computedOptions.value.radius.every((item) => item === computedOptions.value.radius[0])
    );

    watch(
      () => flag.value,
      () => {
        radius.value = flag.value ? 'Mixed' : computedOptions.value.radius[0];
      },
      {
        immediate: true
      }
    );

    //#endregion

    // 前后缀(按照前缀图标、前缀文本、后缀图标、后缀文本，依次添加)
    const fixList = [true, true, true, true];

    // 可添加的校验规则列表
    const ruleList = [
      FormRuleType.Required,
      FormRuleType.Email,
      FormRuleType.Url,
      FormRuleType.Tel,
      FormRuleType.Length,
      FormRuleType.Pattern
    ];

    const labelManage = () => {
      handleManage(computedOptions.value, 'label');

      if (computedOptions.value.label) {
        computedOptions.value.label.text = '文本输入';
      }
    };

    return {
      computedOptions,

      showMenuFocus,
      addFocus,
      delFocus,

      radius,
      showRadius,
      radiusChange,

      fixList,

      ruleList,
      labelManage
    };
  }
});
