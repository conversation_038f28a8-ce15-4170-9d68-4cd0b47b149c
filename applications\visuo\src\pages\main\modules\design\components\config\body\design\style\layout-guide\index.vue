<template>
  <div class="vis-layout-guide">
    <div class="vis-form-inline" v-for="(lg, index) in layoutGuides" :key="index">
      <div class="flex w-full">
        <q-btn @click.stop="onShowPopup($event, lg, index)" :class="{ active: activeIndex === index }">
          <ht-icon class="cursor-pointer vis-icon" name="hticon-vis-layout-grid"></ht-icon>
        </q-btn>
        <div class="flex-1 row items-center pl-1">
          <vis-select v-model="lg.type" :options="types" @change="onChangeType">
            <template v-slot:selected>
              <template v-if="lg.type === LayoutGuideType.Grid">网格 {{ lg.count }}px</template>
              <template v-else>
                {{ lg.count }}{{ lg.type === LayoutGuideType.Row ? '行' : '列' }}
                {{ lg.width === 'Auto' ? '自动' : `${lg.width}px` }}
              </template>
            </template>
          </vis-select>
        </div>
        <div>
          <q-btn class="suffix-button" @click="lg.visible = !lg.visible" flat dense>
            <ht-icon class="vis-icon" :name="lg.visible ? 'hticon-vis-eye-o' : 'hticon-vis-eye-c'" />
          </q-btn>
          <q-btn class="suffix-button" @click="onDelete(index)" flat dense>
            <ht-icon class="vis-icon" name="vis-remove" />
          </q-btn>
        </div>
      </div>
    </div>

    <!-- 弹窗 -->
    <vis-popup ref="popupRef" @before-hide="onHidePopup" :target="false" v-if="activeLayoutGuide">
      <template v-slot:title>
        <vis-select
          v-model="activeLayoutGuide.type"
          :options="types"
          @change="onChangeType"
          class="pl-0 !outline-none"
        ></vis-select>
      </template>
      <div class="vis-form-inline">
        <div class="vis-form-field">
          <div class="vis-form-field__label">
            {{ activeLayoutGuide.type === LayoutGuideType.Grid ? '尺寸' : '数量' }}
          </div>
          <div class="vis-form-field__content">
            <vis-number v-model="activeLayoutGuide.count" :min="1" :precision="0" />
          </div>
        </div>
        <div class="vis-form-field">
          <div class="vis-form-field__label">颜色</div>
          <div class="vis-form-field__content">
            <vis-fill v-model="activeLayoutGuide.fillPaints" :showEyes="false" :onlyColor="true" :minusWidth="0" />
          </div>
        </div>
        <template v-if="activeLayoutGuide.type !== LayoutGuideType.Grid">
          <div class="vis-form-field">
            <div class="vis-form-field__label">布局方式</div>
            <div class="vis-form-field__content">
              <vis-select
                v-model="activeLayoutGuide.pattern"
                :options="activeLayoutGuide.type === LayoutGuideType.Row ? patternModeRows : patternModeCols"
                class="w-full"
                @change="onChangePattern"
              ></vis-select>
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__label">
              {{ activeLayoutGuide.type === LayoutGuideType.Column ? '列宽' : '行高' }}
            </div>
            <div class="vis-form-field__content">
              <q-input
                v-if="activeLayoutGuide.pattern === ItemsAlign.Stretch"
                model-value="自动"
                class="vis-input w-full rounded-borders px-2"
                borderless
                dense
                disable
              />
              <vis-number
                v-else
                v-model="activeLayoutGuide.width"
                :icon="
                  activeLayoutGuide.type === LayoutGuideType.Column
                    ? 'hticon-vis-resize-fixed-x'
                    : 'hticon-vis-resize-fixed-y'
                "
                :min="1"
                :precision="0"
              />
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__label">槽宽</div>
            <div class="vis-form-field__content">
              <vis-number
                v-model="activeLayoutGuide.gap"
                :icon="
                  activeLayoutGuide.type === LayoutGuideType.Column
                    ? 'hticon-vis-gap-horizontal'
                    : 'hticon-vis-gap-vertical'
                "
                :min="0"
                :precision="0"
              />
            </div>
          </div>
          <div class="vis-form-field">
            <div class="vis-form-field__label">边距</div>
            <div class="vis-form-field__content">
              <vis-number
                v-model="activeLayoutGuide.offset"
                :disabled="activeLayoutGuide.pattern === ItemsAlign.Center"
                :icon="
                  activeLayoutGuide.type === LayoutGuideType.Column ? 'hticon-vis-padding-x' : 'hticon-vis-padding-y'
                "
                :min="0"
                :precision="0"
              />
            </div>
          </div>
        </template>
      </div>
    </vis-popup>
  </div>
</template>
<script lang="ts" src="./index.ts"></script>
<style lang="scss" src="./index.scss"></style>
