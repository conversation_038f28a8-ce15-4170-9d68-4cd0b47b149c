<template>
  <div class="vis-config-card">
    <div class="vis-config-card__header">
      <span> {{ title || '辅助' }} </span>
      <q-btn flat dense v-if="addShow">
        <ht-icon class="vis-icon" name="vis-add" />
        <q-menu v-model="showMenuFix" style="width: 120px" class="vis-menu" dense>
          <q-list dense>
            <q-item
              v-if="fixList[0]"
              :disable="hasFix('prefix', 'icon')"
              @click="addFix('prefix', 'icon')"
              clickable
              v-close-popup
            >
              <q-item-section>前缀图标</q-item-section>
            </q-item>
            <q-item
              v-if="fixList[1]"
              :disable="hasFix('prefix', 'text')"
              @click="addFix('prefix', 'text')"
              clickable
              v-close-popup
            >
              <q-item-section>前缀文本</q-item-section>
            </q-item>
            <q-item
              v-if="fixList[2]"
              :disable="hasFix('suffix', 'icon')"
              @click="addFix('suffix', 'icon')"
              clickable
              v-close-popup
            >
              <q-item-section>后缀图标</q-item-section>
            </q-item>
            <q-item
              v-if="fixList[3]"
              :disable="hasFix('suffix', 'text')"
              @click="addFix('suffix', 'text')"
              clickable
              v-close-popup
            >
              <q-item-section>后缀文本</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>
    <div class="vis-config-card__body">
      <!-- 前缀图标 -->
      <div class="vis-form-inline" v-if="hasFix('prefix', 'icon')">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">前缀图标</div>
            <div class="vis-form-field__content">
              <vis-form-fix-item :options="computedPrefix[0]" type="icon" />
            </div>
          </div>
        </div>
        <q-btn class="btn-field" @click="delFix('prefix', 'icon')">
          <ht-icon class="vis-icon" name="vis-subtract" />
        </q-btn>
      </div>
      <!-- 前缀文本 -->
      <div class="vis-form-inline" v-if="hasFix('prefix', 'text')">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">前缀文本</div>
            <div class="vis-form-field__content">
              <vis-form-fix-item :options="computedPrefix[computedPrefix.length - 1]" type="text" />
            </div>
          </div>
        </div>
        <q-btn class="btn-field" @click="delFix('prefix', 'text')">
          <ht-icon class="vis-icon" name="vis-subtract" />
        </q-btn>
      </div>
      <!-- 后缀图标 -->
      <div class="vis-form-inline" v-if="hasFix('suffix', 'icon')">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">后缀图标</div>
            <div class="vis-form-field__content">
              <vis-form-fix-item :options="computedSuffix[0]" type="icon" />
            </div>
          </div>
        </div>
        <q-btn class="btn-field" @click="delFix('suffix', 'icon')">
          <ht-icon class="vis-icon" name="vis-subtract" />
        </q-btn>
      </div>
      <!-- 后缀文本 -->
      <div class="vis-form-inline" v-if="hasFix('suffix', 'text')">
        <div class="vis-form-inline__content--minus-32">
          <div class="vis-form-field">
            <div class="vis-form-field__label">后缀文本</div>
            <div class="vis-form-field__content">
              <vis-form-fix-item :options="computedSuffix[computedSuffix.length - 1]" type="text" />
            </div>
          </div>
        </div>
        <q-btn class="btn-field" @click="delFix('suffix', 'text')">
          <ht-icon class="vis-icon" name="vis-subtract" />
        </q-btn>
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./fix.ts"></script>
