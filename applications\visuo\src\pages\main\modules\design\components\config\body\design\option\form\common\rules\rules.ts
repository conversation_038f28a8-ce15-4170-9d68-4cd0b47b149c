import { FormRule, FormRuleOptions, FormRuleType } from '@vis/document-core';
import { computed, defineComponent, ref, type PropType } from 'vue';
import { isString } from 'lodash-es';
/**
 * 表单校验规则组件
 * <AUTHOR>
 */
export default defineComponent({
  name: 'vis-config-form-rules',
  props: {
    title: {
      type: String
    },
    options: {
      type: Object as PropType<FormRule[]>,
      required: true
    },
    ruleList: {
      type: Array as PropType<FormRuleType[]>,
      required: true
    }
  },
  setup(props) {
    // 当前已添加的规则列表
    const computedOptions = computed(() => props.options);

    const showMenuRule = ref(false);

    // 当前组件允许的校验规则列表
    const ruleOptions = FormRuleOptions.filter((item) => props.ruleList.includes(item.value));

    // 点击加号展示的规则列表
    const addOptions = computed(() => {
      const list = [];
      const requiredOption = ruleOptions.find((item) => item.value === FormRuleType.Required);
      if (requiredOption) {
        list.push(requiredOption);
      }
      const lengthOption = ruleOptions.find((item) => item.value === FormRuleType.Length);
      if (lengthOption) {
        list.push(lengthOption);
      }
      const rangeOption = ruleOptions.find((item) => item.value === FormRuleType.Range);
      if (rangeOption) {
        list.push(rangeOption);
      }
      if (patternList.value.length > 0) {
        const patternOption = patternList.value.find((item) => item.value === FormRuleType.Pattern);
        // 默认添加自定义模式，否则添加第一个
        const value = patternOption ? patternOption.value : patternList.value[0].value;
        list.push({ label: '模式', value });
      }
      return list;
    });

    const hasModelRule = computed(() => {
      return computedOptions.value.some(
        (item) => ![FormRuleType.Required, FormRuleType.Length, FormRuleType.Range].includes(item.type)
      );
    });

    const hasAllRule = computed(() => {
      return computedOptions.value.length >= addOptions.value.length;
    });

    /**
     * 允许选择的模式列表
     */
    const patternList = computed(() => {
      return ruleOptions
        .filter((item) => ![FormRuleType.Required, FormRuleType.Length, FormRuleType.Range].includes(item.value))
        .map((item) => {
          return {
            ...item,
            disable: computedOptions.value.some((rule) => rule.type === item.value)
          };
        });
    });

    /**
     * 按照长度、其他、必填项顺序添加规则
     * @param value
     */
    const addRule = (value: FormRuleType) => {
      const ruleValue =
        value === FormRuleType.Length
          ? [0, 10]
          : value === FormRuleType.Range
          ? [0, 1000]
          : value === FormRuleType.Pattern
          ? '*'
          : undefined;
      if ([FormRuleType.Length, FormRuleType.Range].includes(value)) {
        computedOptions.value.unshift(new FormRule(value, ruleValue));
      } else {
        if (hasRules(FormRuleType.Required)) {
          // 如果已存在必填项，插入到必填项之前
          computedOptions.value.splice(
            computedOptions.value.findIndex((item) => item.type === FormRuleType.Required),
            0,
            new FormRule(value, ruleValue)
          );
        } else {
          computedOptions.value.push(new FormRule(value, ruleValue));
        }
      }
    };

    const handleRule = (rule: FormRule) => {
      if (rule.type === FormRuleType.Pattern) {
        rule.pattern = '';
      } else {
        delete rule.pattern;
      }
    };

    const delRule = (index: number) => {
      computedOptions.value.splice(index, 1);
      showMenuRule.value = false;
    };

    const hasRules = (value: FormRuleType) => {
      if ([FormRuleType.Required, FormRuleType.Length, FormRuleType.Range].includes(value)) {
        return computedOptions.value.some((item) => item.type === value);
      } else {
        return hasModelRule.value;
      }
    };

    return {
      isString,
      FormRuleType,
      computedOptions,
      showMenuRule,
      ruleOptions,
      addOptions,
      hasModelRule,
      hasAllRule,
      patternList,

      addRule,
      handleRule,
      delRule,
      hasRules
    };
  }
});
